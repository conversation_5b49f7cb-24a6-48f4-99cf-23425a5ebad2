2025-07-11 16:38:33.901 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-11 16:38:33.930 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 45621 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by z<PERSON><PERSON><PERSON> in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-11 16:38:33.931 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-07-11 16:38:34.299 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=1a1f97c8-316b-3a1a-9e9e-471e42aadff8
2025-07-11 16:38:34.305 WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext:635 - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Invalid bean definition with name 'com.jd.jdt.joylaw.ai.mcp.rpc.feign.JoyLawService' defined in null: Could not resolve placeholder 'rpc.joylaw.url' in value "http://${rpc.joylaw.url}"
2025-07-11 16:38:34.313 INFO  o.s.b.a.l.ConditionEvaluationReportLogger:82 - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-11 16:38:34.320 ERROR o.s.b.SpringApplication:857 - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Invalid bean definition with name 'com.jd.jdt.joylaw.ai.mcp.rpc.feign.JoyLawService' defined in null: Could not resolve placeholder 'rpc.joylaw.url' in value "http://${rpc.joylaw.url}"
	at org.springframework.beans.factory.config.PlaceholderConfigurerSupport.doProcessProperties(PlaceholderConfigurerSupport.java:247)
	at org.springframework.context.support.PropertySourcesPlaceholderConfigurer.processProperties(PropertySourcesPlaceholderConfigurer.java:208)
	at org.springframework.context.support.PropertySourcesPlaceholderConfigurer.postProcessBeanFactory(PropertySourcesPlaceholderConfigurer.java:173)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:363)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:189)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:791)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:609)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.refresh(ReactiveWebServerApplicationContext.java:66)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.jd.jdt.joylaw.ai.mcp.JoyLawMcpServerApplication.main(JoyLawMcpServerApplication.java:21)
Caused by: org.springframework.util.PlaceholderResolutionException: Could not resolve placeholder 'rpc.joylaw.url' in value "http://${rpc.joylaw.url}"
	at org.springframework.util.PlaceholderResolutionException.withValue(PlaceholderResolutionException.java:81)
	at org.springframework.util.PlaceholderParser$ParsedValue.resolve(PlaceholderParser.java:423)
	at org.springframework.util.PlaceholderParser.replacePlaceholders(PlaceholderParser.java:128)
	at org.springframework.util.PropertyPlaceholderHelper.parseStringValue(PropertyPlaceholderHelper.java:118)
	at org.springframework.util.PropertyPlaceholderHelper.replacePlaceholders(PropertyPlaceholderHelper.java:114)
	at org.springframework.core.env.AbstractPropertyResolver.doResolvePlaceholders(AbstractPropertyResolver.java:255)
	at org.springframework.core.env.AbstractPropertyResolver.resolveRequiredPlaceholders(AbstractPropertyResolver.java:226)
	at org.springframework.context.support.PropertySourcesPlaceholderConfigurer.lambda$processProperties$0(PropertySourcesPlaceholderConfigurer.java:201)
	at org.springframework.beans.factory.config.BeanDefinitionVisitor.resolveStringValue(BeanDefinitionVisitor.java:293)
	at org.springframework.beans.factory.config.BeanDefinitionVisitor.resolveValue(BeanDefinitionVisitor.java:219)
	at org.springframework.beans.factory.config.BeanDefinitionVisitor.visitPropertyValues(BeanDefinitionVisitor.java:147)
	at org.springframework.beans.factory.config.BeanDefinitionVisitor.visitBeanDefinition(BeanDefinitionVisitor.java:85)
	at org.springframework.beans.factory.config.PlaceholderConfigurerSupport.doProcessProperties(PlaceholderConfigurerSupport.java:244)
	... 13 common frames omitted
2025-07-11 16:38:59.781 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-11 16:38:59.806 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 45658 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-11 16:38:59.807 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-07-11 16:39:00.137 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=385a9520-b5eb-3ecd-9ce1-193c3ec6ce65
2025-07-11 16:39:00.545 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-11 16:39:00.556 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.059 seconds (process running for 1.272)
2025-07-11 16:40:56.249 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-06-18, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-07-11 16:40:56.249 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-06-18, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-07-11 16:40:56.253 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-06-18, so the server will sugggest the 2024-11-05 version instead
2025-07-11 16:40:56.253 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-06-18, so the server will sugggest the 2024-11-05 version instead
2025-07-11 16:40:59.086 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-06-18, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-07-11 16:40:59.087 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-06-18, so the server will sugggest the 2024-11-05 version instead
2025-07-11 16:51:17.256 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-06-18, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-07-11 16:51:17.258 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-06-18, so the server will sugggest the 2024-11-05 version instead
2025-07-11 16:53:13.597 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-11 16:53:19.460 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-11 16:53:19.491 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 46877 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-11 16:53:19.492 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-07-11 16:53:19.816 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=385a9520-b5eb-3ecd-9ce1-193c3ec6ce65
2025-07-11 16:53:20.241 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-11 16:53:20.252 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.057 seconds (process running for 1.349)
2025-07-11 16:53:32.411 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-06-18, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-07-11 16:53:32.414 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-06-18, so the server will sugggest the 2024-11-05 version instead
2025-07-11 16:56:48.457 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-06-18, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-07-11 16:56:48.460 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-06-18, so the server will sugggest the 2024-11-05 version instead
2025-07-11 17:00:03.176 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-11 17:00:07.684 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-11 17:00:07.711 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 47551 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-11 17:00:07.712 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-07-11 17:00:08.029 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=385a9520-b5eb-3ecd-9ce1-193c3ec6ce65
2025-07-11 17:00:08.445 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-11 17:00:08.456 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.037 seconds (process running for 1.24)
2025-07-11 17:00:45.451 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-06-18, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-07-11 17:00:45.456 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-06-18, so the server will sugggest the 2024-11-05 version instead
2025-07-11 17:05:11.187 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-11 17:05:14.668 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-11 17:05:14.695 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 47921 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-11 17:05:14.696 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-07-11 17:05:15.005 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=385a9520-b5eb-3ecd-9ce1-193c3ec6ce65
2025-07-11 17:05:15.421 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-11 17:05:15.432 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.025 seconds (process running for 1.256)
2025-07-11 17:05:20.347 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-06-18, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-07-11 17:05:20.352 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-06-18, so the server will sugggest the 2024-11-05 version instead
2025-07-11 17:20:26.978 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-11 17:20:57.073 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-11 17:20:57.100 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 49257 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-11 17:20:57.101 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-07-11 17:20:57.425 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=385a9520-b5eb-3ecd-9ce1-193c3ec6ce65
2025-07-11 17:20:57.864 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-11 17:20:57.874 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.068 seconds (process running for 1.284)
2025-07-11 17:21:02.750 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-06-18, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-07-11 17:21:02.754 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-06-18, so the server will sugggest the 2024-11-05 version instead
2025-07-11 17:24:12.352 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-11 17:24:15.733 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-11 17:24:15.758 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 49424 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-11 17:24:15.758 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-07-11 17:24:16.066 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=385a9520-b5eb-3ecd-9ce1-193c3ec6ce65
2025-07-11 17:24:16.473 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-11 17:24:16.485 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.987 seconds (process running for 1.201)
2025-07-11 17:24:19.998 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-06-18, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-07-11 17:24:20.001 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-06-18, so the server will sugggest the 2024-11-05 version instead
2025-07-11 17:38:57.138 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-11 17:39:00.474 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-11 17:39:00.500 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 50827 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-11 17:39:00.501 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-07-11 17:39:00.853 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=385a9520-b5eb-3ecd-9ce1-193c3ec6ce65
2025-07-11 17:39:01.281 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-11 17:39:01.299 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.089 seconds (process running for 1.294)
2025-07-11 17:47:17.780 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-06-18, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-07-11 17:47:17.780 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-06-18, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-07-11 17:47:17.785 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-06-18, so the server will sugggest the 2024-11-05 version instead
2025-07-11 17:47:17.785 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-06-18, so the server will sugggest the 2024-11-05 version instead
2025-07-11 17:47:20.667 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-06-18, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-07-11 17:47:20.668 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-06-18, so the server will sugggest the 2024-11-05 version instead
2025-07-11 19:35:49.372 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-11 19:35:49.377 INFO  o.s.b.w.e.n.GracefulShutdown:62 - Graceful shutdown complete
