2025-07-28 10:35:55.056 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-07-28 10:35:55.081 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 5211 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by <PERSON><PERSON><PERSON><PERSON> in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 10:35:55.082 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 10:35:55.365 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=76911dab-c6aa-3488-b3f0-fb50294b6875
2025-07-28 10:35:55.839 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 10:35:55.847 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.989 seconds (process running for 1.232)
2025-07-28 10:35:58.608 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 10:36:28.618 INFO  o.s.c.s.DefaultLifecycleProcessor:536 - Shutdown phase 2147482623 ends with 1 bean still running after timeout of 30000ms: [webServerGracefulShutdown]
2025-07-28 10:36:28.641 INFO  o.s.b.w.e.n.GracefulShutdown:66 - Graceful shutdown aborted with one or more requests still active
2025-07-28 10:36:34.652 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-07-28 10:36:34.679 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 5266 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 10:36:34.680 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 10:36:35.028 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=76911dab-c6aa-3488-b3f0-fb50294b6875
2025-07-28 10:36:35.573 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 10:36:35.581 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.124 seconds (process running for 1.695)
2025-07-28 10:37:49.310 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 10:38:01.066 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-07-28 10:38:01.104 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 5475 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 10:38:01.104 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 10:38:01.408 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=76911dab-c6aa-3488-b3f0-fb50294b6875
2025-07-28 10:38:01.934 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 10:38:01.941 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.111 seconds (process running for 2.345)
2025-07-28 10:41:48.128 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 10:41:55.863 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-07-28 10:41:55.893 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 5908 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 10:41:55.893 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 10:41:56.180 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=76911dab-c6aa-3488-b3f0-fb50294b6875
2025-07-28 10:41:56.679 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 10:41:56.687 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.007 seconds (process running for 1.446)
2025-07-28 10:43:58.893 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 10:57:25.510 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-07-28 10:57:25.541 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 7339 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 10:57:25.541 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 10:57:25.838 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=8e0bef25-c3b2-3ec4-b4bc-91b6e4b9e3c9
2025-07-28 10:57:26.384 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 10:57:26.393 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.096 seconds (process running for 1.54)
2025-07-28 11:03:30.203 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-28 11:03:30.235 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 8271 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 11:03:30.236 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 11:03:30.644 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=54c85936-7e49-3172-a05c-85faafed748d
2025-07-28 11:03:33.331 WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext:635 - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-07-28 11:03:33.340 INFO  o.s.b.a.l.ConditionEvaluationReportLogger:82 - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-28 11:03:33.351 ERROR o.s.b.d.LoggingFailureAnalysisReporter:40 - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-07-28 11:08:05.551 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 11:08:11.289 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-28 11:08:11.318 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 8842 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 11:08:11.319 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 11:08:11.664 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=15ba3f2d-4efc-39ee-a438-5feb6ca1e022
2025-07-28 11:08:12.246 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 11:08:12.257 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.21 seconds (process running for 2.136)
2025-07-28 11:09:46.473 INFO  i.m.s.McpAsyncServer:197 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=JoyCode, version=1.0.0]
2025-07-28 11:09:46.477 WARN  i.m.s.McpAsyncServer:213 - Client requested unsupported protocol version: 2025-03-26, so the server will suggest the 2024-11-05 version instead
2025-07-28 11:11:22.948 INFO  i.m.s.McpAsyncServer:197 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=JoyCode, version=1.0.0]
2025-07-28 11:11:22.950 WARN  i.m.s.McpAsyncServer:213 - Client requested unsupported protocol version: 2025-03-26, so the server will suggest the 2024-11-05 version instead
2025-07-28 11:11:42.988 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-28 11:11:43.031 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 9332 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 11:11:43.031 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 11:11:43.438 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=15ba3f2d-4efc-39ee-a438-5feb6ca1e022
2025-07-28 11:11:44.127 WARN  o.s.c.s.DefaultLifecycleProcessor:410 - Failed to stop bean 'reactorResourceFactory'
reactor.core.Exceptions$ReactiveException: java.lang.InterruptedException
	at reactor.core.Exceptions.propagate(Exceptions.java:410)
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:96)
	at reactor.core.publisher.Mono.block(Mono.java:1779)
	at org.springframework.http.client.ReactorResourceFactory.stop(ReactorResourceFactory.java:298)
	at org.springframework.context.SmartLifecycle.stop(SmartLifecycle.java:120)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStop(DefaultLifecycleProcessor.java:384)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.stop(DefaultLifecycleProcessor.java:526)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.stopBeans(DefaultLifecycleProcessor.java:353)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:245)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:1006)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:630)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.refresh(ReactiveWebServerApplicationContext.java:66)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.jd.jdt.joylaw.ai.mcp.JoyLawMcpServerApplication.main(JoyLawMcpServerApplication.java:21)
Caused by: java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer.acquireSharedInterruptibly(AbstractQueuedSynchronizer.java:1048)
	at java.base/java.util.concurrent.CountDownLatch.await(CountDownLatch.java:230)
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:91)
	... 17 common frames omitted
2025-07-28 11:11:44.128 WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext:635 - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-07-28 11:11:51.775 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-28 11:11:51.821 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 9339 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 11:11:51.822 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 11:11:52.205 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=15ba3f2d-4efc-39ee-a438-5feb6ca1e022
2025-07-28 11:11:54.863 WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext:635 - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-07-28 11:11:54.877 INFO  o.s.b.a.l.ConditionEvaluationReportLogger:82 - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-28 11:11:54.889 ERROR o.s.b.d.LoggingFailureAnalysisReporter:40 - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-07-28 11:12:05.464 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 11:12:14.408 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-28 11:12:14.437 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 9359 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 11:12:14.438 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 11:12:14.827 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=15ba3f2d-4efc-39ee-a438-5feb6ca1e022
2025-07-28 11:12:15.410 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 11:12:15.422 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.302 seconds (process running for 2.276)
2025-07-28 11:12:56.571 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 11:13:02.999 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-28 11:13:03.027 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 9415 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 11:13:03.027 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 11:13:03.361 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=15ba3f2d-4efc-39ee-a438-5feb6ca1e022
2025-07-28 11:13:03.931 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 11:13:03.942 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.238 seconds (process running for 1.607)
2025-07-28 11:13:09.726 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 11:13:17.228 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-28 11:13:17.260 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 9485 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 11:13:17.260 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-07-28 11:13:17.690 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=15ba3f2d-4efc-39ee-a438-5feb6ca1e022
2025-07-28 11:13:18.367 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 11:13:18.381 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.632 seconds (process running for 2.176)
2025-07-28 11:13:34.258 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 11:13:44.041 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-28 11:13:44.091 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 9562 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 11:13:44.091 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-07-28 11:13:44.487 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=15ba3f2d-4efc-39ee-a438-5feb6ca1e022
2025-07-28 11:13:45.126 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 11:13:45.137 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.498 seconds (process running for 1.926)
2025-07-28 11:14:06.761 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 11:14:16.374 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-28 11:14:16.405 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 9663 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 11:14:16.406 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 11:14:16.778 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=15ba3f2d-4efc-39ee-a438-5feb6ca1e022
2025-07-28 11:14:17.367 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 11:14:17.378 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.275 seconds (process running for 1.638)
2025-07-28 11:14:23.436 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 11:14:32.368 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-28 11:14:32.421 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 9756 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 11:14:32.422 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 11:14:32.861 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=15ba3f2d-4efc-39ee-a438-5feb6ca1e022
2025-07-28 11:14:33.495 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 11:14:33.507 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.549 seconds (process running for 2.027)
2025-07-28 11:15:57.990 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 11:16:11.534 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-28 11:16:11.562 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 10070 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 11:16:11.563 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 11:16:11.897 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=15ba3f2d-4efc-39ee-a438-5feb6ca1e022
2025-07-28 11:16:12.471 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 11:16:12.483 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.217 seconds (process running for 1.623)
2025-07-28 11:16:26.714 INFO  i.m.s.McpAsyncServer:197 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=JoyCode, version=1.0.0]
2025-07-28 11:16:26.718 WARN  i.m.s.McpAsyncServer:213 - Client requested unsupported protocol version: 2025-03-26, so the server will suggest the 2024-11-05 version instead
2025-07-28 11:19:09.799 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 11:19:14.124 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-28 11:19:14.158 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 10591 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 11:19:14.159 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 11:19:14.521 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=15ba3f2d-4efc-39ee-a438-5feb6ca1e022
2025-07-28 11:19:15.096 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 11:19:15.108 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.219 seconds (process running for 1.583)
2025-07-28 11:22:11.144 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-28 11:22:11.166 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:53 - Starting OfficeConvertUltimateQualityTest using Java 17.0.15 with PID 10872 (started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 11:22:11.166 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:658 - The following 1 profile is active: "local"
2025-07-28 11:22:11.451 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=6b445178-b912-30be-ac71-0c27f659b4a4
2025-07-28 11:22:11.918 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:59 - Started OfficeConvertUltimateQualityTest in 0.965 seconds (process running for 1.48)
2025-07-28 11:22:12.148 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:32 - === 开始终极质量Word转PDF转换测试 ===
2025-07-28 11:22:12.156 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:44 - ✓ 读取Word文件成功
2025-07-28 11:22:12.158 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:45 -   - 文件路径: src/main/resources/static/中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 11:22:12.158 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:46 -   - 文件大小: 14817280 bytes (14470 KB)
2025-07-28 11:22:12.158 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:55 - 开始执行终极质量Word转PDF转换...
2025-07-28 11:22:12.158 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:153 - ============================================================
2025-07-28 11:22:12.158 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:154 - ========== 字体系统诊断开始 ==========
2025-07-28 11:22:12.158 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:155 - 诊断时间: 2025-07-28 11:22:12
2025-07-28 11:22:12.158 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:156 - ============================================================
2025-07-28 11:22:12.158 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:174 - ▶ [环境诊断] 开始环境诊断检查...
2025-07-28 11:22:12.158 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:183 - [INFO] 运行环境信息:
2025-07-28 11:22:12.158 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:184 -   • 操作系统: Mac OS X 15.5
2025-07-28 11:22:12.159 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:185 -   • Java版本: 17.0.15 (Microsoft)
2025-07-28 11:22:12.159 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:186 -   • 工作目录: /Users/<USER>/IdeaProjects/mcp-server
2025-07-28 11:22:12.159 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:190 -   • 运行环境: 本地环境
2025-07-28 11:22:12.159 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:211 - [INFO] 临时目录状态:
2025-07-28 11:22:12.159 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:212 -   • 临时目录路径: /var/folders/qs/761__31103sc01c7b0m4vxth0000gp/T/
2025-07-28 11:22:12.159 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:213 -   • 目录存在: ✓
2025-07-28 11:22:12.159 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:214 -   • 目录可读: ✓
2025-07-28 11:22:12.159 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:215 -   • 目录可写: ✓
2025-07-28 11:22:12.160 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:223 -   • 可用空间: 155418.03 MB / 471482.08 MB
2025-07-28 11:22:12.160 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:239 - [INFO] Java字体系统属性:
2025-07-28 11:22:12.160 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • java.awt.fonts: 未设置
2025-07-28 11:22:12.160 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • sun.java2d.fontpath: 未设置
2025-07-28 11:22:12.160 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • java.awt.headless: true
2025-07-28 11:22:12.160 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • file.encoding: UTF-8
2025-07-28 11:22:12.160 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • sun.jnu.encoding: UTF-8
2025-07-28 11:22:12.160 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:261 - [INFO] 字体相关环境变量:
2025-07-28 11:22:12.160 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FONTCONFIG_PATH: 未设置
2025-07-28 11:22:12.160 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FONTCONFIG_FILE: 未设置
2025-07-28 11:22:12.160 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FC_DEBUG: 未设置
2025-07-28 11:22:12.160 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:280 - 检查系统字体目录:
2025-07-28 11:22:12.161 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   /usr/share/fonts - 不存在
2025-07-28 11:22:12.162 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   /usr/local/share/fonts - 不存在
2025-07-28 11:22:12.163 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:288 -   /System/Library/Fonts - 存在 (81 个字体文件)
2025-07-28 11:22:12.164 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:288 -   /Library/Fonts - 存在 (1 个字体文件)
2025-07-28 11:22:12.164 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   C:\Windows\Fonts - 不存在
2025-07-28 11:22:12.164 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:201 - ✓ [环境诊断] 环境诊断检查完成
2025-07-28 11:22:12.164 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:309 - --- 字体文件设置开始 ---
2025-07-28 11:22:12.164 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:319 - 字体资源URL: file:/Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts, 协议: file
2025-07-28 11:22:12.164 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:326 - 本地文件系统字体路径: /Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts
2025-07-28 11:22:12.164 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:622 - --- 字体文件分析开始 ---
2025-07-28 11:22:12.166 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:660 - 字体文件统计:
2025-07-28 11:22:12.166 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ini 格式: 1 个文件
2025-07-28 11:22:12.166 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .fon 格式: 192 个文件
2025-07-28 11:22:12.166 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ttc 格式: 14 个文件
2025-07-28 11:22:12.166 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .zip 格式: 1 个文件
2025-07-28 11:22:12.166 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ttf 格式: 146 个文件
2025-07-28 11:22:12.166 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .dat 格式: 1 个文件
2025-07-28 11:22:12.166 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .xml 格式: 1 个文件
2025-07-28 11:22:12.166 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:664 - 发现的优先中文字体: [Dengb.ttf, SimsunExtG.ttf, NotoSerifSC-VF.ttf, Deng.ttf, NotoSansSC-VF.ttf, HYZhongHeiTi-197.ttf, Dengl.ttf]
2025-07-28 11:22:12.166 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:665 - 发现的优先英文字体: [times.ttf, consola.ttf, arial.ttf, calibri.ttf]
2025-07-28 11:22:12.166 WARN  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:670 - 发现 192 个 .fon 格式字体文件，在Linux环境下可能不兼容
2025-07-28 11:22:12.166 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:673 - --- 字体文件分析完成 ---
2025-07-28 11:22:12.166 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:344 - --- 字体文件设置完成 ---
2025-07-28 11:22:12.167 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:680 - --- 高级字体设置开始 ---
2025-07-28 11:22:12.268 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:694 - 添加字体文件夹源: /Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts
2025-07-28 11:22:12.269 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:700 - 添加系统字体源
2025-07-28 11:22:12.278 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:718 - 配置字体替换规则...
2025-07-28 11:22:12.278 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:741 - 字体替换规则配置完成
2025-07-28 11:22:12.279 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:748 - 配置字体回退设置...
2025-07-28 11:22:12.280 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:755 - 已加载Noto字体回退设置
2025-07-28 11:22:12.280 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:763 - 字体回退设置配置完成
2025-07-28 11:22:12.280 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:711 - --- 高级字体设置完成 ---
2025-07-28 11:22:12.280 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:779 - --- 字体加载状态验证开始 ---
2025-07-28 11:22:12.281 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:785 - 配置的字体源数量: 2
2025-07-28 11:22:12.281 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:789 - 字体源 1: com.aspose.words.FolderFontSource@62f7d7bd (类型: FolderFontSource)
2025-07-28 11:22:12.281 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:793 -   字体源类型: FolderFontSource
2025-07-28 11:22:12.281 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:794 -   字体源配置完成
2025-07-28 11:22:12.281 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:789 - 字体源 2: com.aspose.words.SystemFontSource@de63949 (类型: SystemFontSource)
2025-07-28 11:22:12.281 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:793 -   字体源类型: SystemFontSource
2025-07-28 11:22:12.281 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:794 -   字体源配置完成
2025-07-28 11:22:12.281 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:812 - 测试字体解析能力:
2025-07-28 11:22:12.281 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'SimSun' - 解析测试通过
2025-07-28 11:22:12.281 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 '宋体' - 解析测试通过
2025-07-28 11:22:12.281 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'Microsoft YaHei' - 解析测试通过
2025-07-28 11:22:12.281 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 '微软雅黑' - 解析测试通过
2025-07-28 11:22:12.281 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'Arial' - 解析测试通过
2025-07-28 11:22:12.281 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:804 - --- 字体加载状态验证完成 ---
2025-07-28 11:22:12.281 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1350 - ▶ [可用性验证] 开始字体可用性验证...
2025-07-28 11:22:12.281 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1368 - [INFO] 中文字体可用性检查:
2025-07-28 11:22:12.282 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • NotoSansSC-VF - ✓可用 (直接字体文件)
2025-07-28 11:22:12.282 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • SimSun - ✓可用 (替换为: NotoSansSC-VF.ttf)
2025-07-28 11:22:12.282 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • Microsoft YaHei - ✓可用 (替换为: HYZhongHeiTi-197.ttf)
2025-07-28 11:22:12.282 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • HYZhongHeiTi-197 - ✓可用 (直接字体文件)
2025-07-28 11:22:12.282 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1404 - [INFO] 字体替换规则验证:
2025-07-28 11:22:12.282 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1411 -   • 表格替换规则: ✓已启用
2025-07-28 11:22:12.282 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1414 -   • 字体信息替换: ✓已启用
2025-07-28 11:22:12.282 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1417 -   • 默认字体替换: ✓已启用
2025-07-28 11:22:12.282 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1420 -   • 默认字体: NotoSansSC-VF.ttf
2025-07-28 11:22:12.282 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1432 - [INFO] 字体文件完整性验证:
2025-07-28 11:22:12.282 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1435 -   • 使用本地字体目录，跳过临时文件验证
2025-07-28 11:22:12.282 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1361 - ✓ [可用性验证] 字体可用性验证完成
2025-07-28 11:22:12.282 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:164 - ============================================================
2025-07-28 11:22:12.282 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:165 - ========== 字体系统诊断完成 ==========
2025-07-28 11:22:12.283 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:166 - 完成时间: 2025-07-28 11:22:12
2025-07-28 11:22:12.283 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:167 - ============================================================
2025-07-28 11:22:12.283 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1002 - 开始docx转pdf转换: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 11:22:12.283 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:868 - ============================================================
2025-07-28 11:22:12.283 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:869 - ========== 强制字体系统诊断开始 ==========
2025-07-28 11:22:12.283 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:870 - 诊断时间: 2025-07-28 11:22:12
2025-07-28 11:22:12.283 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:871 - 字体初始化状态: 已初始化
2025-07-28 11:22:12.283 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:872 - ============================================================
2025-07-28 11:22:12.283 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:896 - ▶ [环境检查] 运行环境: 本地环境
2025-07-28 11:22:12.283 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:897 - ▶ [环境检查] 操作系统: Mac OS X
2025-07-28 11:22:12.283 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:898 - ▶ [环境检查] 工作目录: /Users/<USER>/IdeaProjects/mcp-server
2025-07-28 11:22:12.283 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:899 - ▶ [环境检查] 临时目录: /var/folders/qs/761__31103sc01c7b0m4vxth0000gp/T/
2025-07-28 11:22:12.283 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:906 - ▶ [字体文件] 开始字体文件状态检查...
2025-07-28 11:22:12.283 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:931 - [INFO] 使用本地字体目录（非JAR环境）
2025-07-28 11:22:12.283 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:939 - ▶ [字体设置] 开始字体设置验证...
2025-07-28 11:22:12.283 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:945 - [INFO] 配置的字体源数量: 2
2025-07-28 11:22:12.283 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 1: com.aspose.words.FolderFontSource@62f7d7bd (类型: FolderFontSource)
2025-07-28 11:22:12.284 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 2: com.aspose.words.SystemFontSource@de63949 (类型: SystemFontSource)
2025-07-28 11:22:12.284 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:956 - [INFO] 字体替换规则状态:
2025-07-28 11:22:12.284 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:957 -   • 表格替换规则: ✓已启用
2025-07-28 11:22:12.284 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:958 -   • 默认字体替换: ✓已启用
2025-07-28 11:22:12.284 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:960 -   • 默认字体: NotoSansSC-VF.ttf
2025-07-28 11:22:12.284 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1368 - [INFO] 中文字体可用性检查:
2025-07-28 11:22:12.284 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • NotoSansSC-VF - ✓可用 (直接字体文件)
2025-07-28 11:22:12.284 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • SimSun - ✓可用 (替换为: NotoSansSC-VF.ttf)
2025-07-28 11:22:12.284 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • Microsoft YaHei - ✓可用 (替换为: HYZhongHeiTi-197.ttf)
2025-07-28 11:22:12.284 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • HYZhongHeiTi-197 - ✓可用 (直接字体文件)
2025-07-28 11:22:12.284 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:886 - ============================================================
2025-07-28 11:22:12.284 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:887 - ========== 强制字体系统诊断完成 ==========
2025-07-28 11:22:12.284 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:888 - 完成时间: 2025-07-28 11:22:12
2025-07-28 11:22:12.284 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:889 - ============================================================
2025-07-28 11:22:12.284 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1008 - ▶ [文档加载] 开始加载文档: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 11:22:13.227 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1010 - ✓ [文档加载] 文档加载成功，开始应用字体设置
2025-07-28 11:22:13.228 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1014 - ✓ [字体设置] 全局字体设置已应用到文档
2025-07-28 11:22:13.228 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1063 - ▶ [字体分析] 开始分析文档字体使用情况: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 11:22:13.228 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1070 - [INFO] 文档字体源配置: 2 个字体源
2025-07-28 11:22:13.228 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1074 -   字体源 1: FolderFontSource
2025-07-28 11:22:13.228 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1074 -   字体源 2: SystemFontSource
2025-07-28 11:22:13.289 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1104 - [INFO] 文档中使用的字体: 宋体, 仿宋, Times New Roman, Wingdings 2
2025-07-28 11:22:13.290 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1112 - [INFO] 包含中文字体: 是
2025-07-28 11:22:13.290 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1081 - ✓ [字体分析] 文档字体使用情况分析完成
2025-07-28 11:22:13.290 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1021 - ▶ [PDF转换] 开始PDF转换，应用字体嵌入配置...
2025-07-28 11:22:13.290 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1126 - ▶ [PDF字体] 验证PDF字体嵌入设置...
2025-07-28 11:22:13.292 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1135 - [INFO] PDF字体嵌入配置:
2025-07-28 11:22:13.292 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1136 -   • 嵌入完整字体: ✓已启用
2025-07-28 11:22:13.292 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1137 -   • 字体嵌入模式: EMBED_ALL (嵌入所有字体)
2025-07-28 11:22:13.293 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1138 -   • 文本压缩: FLATE
2025-07-28 11:22:13.293 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1025 - ▶ [转换执行] 开始执行docx转pdf转换...
2025-07-28 11:22:20.012 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1027 - ✓ [转换执行] docx转pdf转换执行完成
2025-07-28 11:22:20.015 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1035 - ✓ [转换完成] docx转pdf转换成功完成:
2025-07-28 11:22:20.016 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1036 -   • 源文件: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx (输入大小: 14817280 bytes)
2025-07-28 11:22:20.016 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1037 -   • 目标文件: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.pdf (输出大小: 26627709 bytes)
2025-07-28 11:22:20.016 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1038 -   • 压缩比: 179.71%
2025-07-28 11:22:20.017 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1149 - ▶ [PDF验证] 验证PDF字体嵌入结果...
2025-07-28 11:22:20.017 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1152 - [INFO] PDF文件生成统计:
2025-07-28 11:22:20.017 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1153 -   • PDF文件大小: 26003 KB
2025-07-28 11:22:20.018 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1159 -   • 包含字体信息: ✓是
2025-07-28 11:22:20.018 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:63 - ✓ 终极质量转换完成！
2025-07-28 11:22:20.018 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:64 - === 转换统计信息 ===
2025-07-28 11:22:20.018 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:65 -   - 转换耗时: 7860 ms ({:.2f} 秒)
2025-07-28 11:22:20.018 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:66 -   - 原文件大小: 14817280 bytes (14470 KB)
2025-07-28 11:22:20.018 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:67 -   - PDF文件大小: 26627709 bytes (26003 KB)
2025-07-28 11:22:20.018 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:68 -   - 文件大小比率: {:.2f}%
2025-07-28 11:22:20.018 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:69 -   - 转换速度: {:.2f} KB/s
2025-07-28 11:22:20.026 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:81 - ✓ 终极质量PDF文件已保存到: target/test-output/ultimate_quality_20250728_112220.pdf
2025-07-28 11:22:20.027 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:207 - === 执行详细验证 ===
2025-07-28 11:22:20.027 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:219 - ✓ PDF格式验证通过
2025-07-28 11:22:20.027 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:226 - ✓ 文件大小验证通过: 26627709 bytes
2025-07-28 11:22:20.027 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:231 - ✓ 文件可读性验证通过
2025-07-28 11:22:20.027 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:233 - === 详细验证完成 ===
2025-07-28 11:22:20.027 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:87 - === 终极质量转换测试完成 ===
2025-07-28 11:22:20.027 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:88 - 请检查输出文件: target/test-output/ultimate_quality_20250728_112220.pdf 以验证格式保持效果
2025-07-28 11:22:20.027 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:292 - 
2025-07-28 11:22:20.027 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:293 - === 终极质量PDF检查清单 ===
2025-07-28 11:22:20.027 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:294 - 请手动验证以下格式保持情况（深度优化版）:
2025-07-28 11:22:20.027 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:295 - 
2025-07-28 11:22:20.027 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:296 - 【字体质量检查】
2025-07-28 11:22:20.027 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:297 - □ 1. 中文字体完美显示（宋体、黑体、微软雅黑、楷体、仿宋）
2025-07-28 11:22:20.027 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:298 - □ 2. 英文字体完美显示（Times New Roman、Arial、Calibri、Cambria）
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:299 - □ 3. 字体大小精确保持，无缩放变形
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:300 - □ 4. 字体粗细（加粗、正常）完全一致
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:301 - □ 5. 字体样式（斜体、下划线）完全保持
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:302 - 
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:303 - 【格式质量检查】
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:304 - □ 6. 文字颜色和背景色精确还原
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:305 - □ 7. 段落间距和行距完全一致
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:306 - □ 8. 文本对齐方式（左对齐、居中、右对齐、两端对齐）保持
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:307 - □ 9. 首行缩进和段落缩进精确保持
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:308 - □ 10. 项目符号和编号格式完整
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:309 - 
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:310 - 【表格质量检查】
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:311 - □ 11. 表格边框线条粗细和颜色正确
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:312 - □ 12. 单元格背景色和文字颜色保持
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:313 - □ 13. 表格列宽和行高精确保持
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:314 - □ 14. 单元格内文字对齐方式正确
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:315 - □ 15. 合并单元格格式完整
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:316 - 
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:317 - 【页面布局检查】
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:318 - □ 16. 页面边距完全一致
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:319 - □ 17. 页眉页脚内容和格式完整
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:320 - □ 18. 页码位置和格式正确
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:321 - □ 19. 分页位置准确，无异常分页
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:322 - □ 20. 页面方向（横向/纵向）保持
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:323 - 
2025-07-28 11:22:20.028 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:324 - 【图像质量检查】
2025-07-28 11:22:20.029 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:325 - □ 21. 图片清晰度高，无模糊
2025-07-28 11:22:20.029 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:326 - □ 22. 图片位置和大小精确
2025-07-28 11:22:20.029 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:327 - □ 23. 图片环绕文字方式保持
2025-07-28 11:22:20.029 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:328 - □ 24. 图片颜色和对比度正确
2025-07-28 11:22:20.029 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:329 - 
2025-07-28 11:22:20.029 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:330 - 【功能性检查】
2025-07-28 11:22:20.029 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:331 - □ 25. 超链接可点击且跳转正确
2025-07-28 11:22:20.029 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:332 - □ 26. 书签和目录结构完整
2025-07-28 11:22:20.029 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:333 - □ 27. 交叉引用功能正常
2025-07-28 11:22:20.029 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:334 - □ 28. 脚注和尾注格式正确
2025-07-28 11:22:20.029 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:335 - 
2025-07-28 11:22:20.029 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:336 - 【整体质量检查】
2025-07-28 11:22:20.029 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:337 - □ 29. 整体视觉效果与Word文档100%一致
2025-07-28 11:22:20.029 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:338 - □ 30. 无任何格式丢失或变形
2025-07-28 11:22:20.029 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:339 - □ 31. 文档结构完整，无缺失内容
2025-07-28 11:22:20.029 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:340 - □ 32. PDF文件大小合理，质量优秀
2025-07-28 11:22:20.029 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:341 - 
2025-07-28 11:22:20.029 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:342 - 如果以上所有项目都符合预期，说明终极质量格式保持修复成功！
2025-07-28 11:22:20.029 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:343 - === 检查清单结束 ===
2025-07-28 11:23:00.711 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-28 11:23:00.733 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:53 - Starting OfficeConvertUltimateQualityTest using Java 17.0.15 with PID 10953 (started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 11:23:00.733 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:658 - The following 1 profile is active: "local"
2025-07-28 11:23:01.011 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=6b445178-b912-30be-ac71-0c27f659b4a4
2025-07-28 11:23:01.479 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:59 - Started OfficeConvertUltimateQualityTest in 0.917 seconds (process running for 1.34)
2025-07-28 11:23:01.717 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:32 - === 开始终极质量Word转PDF转换测试 ===
2025-07-28 11:23:01.728 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:44 - ✓ 读取Word文件成功
2025-07-28 11:23:01.730 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:45 -   - 文件路径: src/main/resources/static/中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 11:23:01.731 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:46 -   - 文件大小: 14817280 bytes (14470 KB)
2025-07-28 11:23:01.731 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:55 - 开始执行终极质量Word转PDF转换...
2025-07-28 11:23:01.731 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:153 - ============================================================
2025-07-28 11:23:01.731 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:154 - ========== 字体系统诊断开始 ==========
2025-07-28 11:23:01.731 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:155 - 诊断时间: 2025-07-28 11:23:01
2025-07-28 11:23:01.731 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:156 - ============================================================
2025-07-28 11:23:01.731 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:174 - ▶ [环境诊断] 开始环境诊断检查...
2025-07-28 11:23:01.731 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:183 - [INFO] 运行环境信息:
2025-07-28 11:23:01.731 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:184 -   • 操作系统: Mac OS X 15.5
2025-07-28 11:23:01.731 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:185 -   • Java版本: 17.0.15 (Microsoft)
2025-07-28 11:23:01.732 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:186 -   • 工作目录: /Users/<USER>/IdeaProjects/mcp-server
2025-07-28 11:23:01.732 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:190 -   • 运行环境: 本地环境
2025-07-28 11:23:01.732 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:211 - [INFO] 临时目录状态:
2025-07-28 11:23:01.732 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:212 -   • 临时目录路径: /var/folders/qs/761__31103sc01c7b0m4vxth0000gp/T/
2025-07-28 11:23:01.732 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:213 -   • 目录存在: ✓
2025-07-28 11:23:01.732 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:214 -   • 目录可读: ✓
2025-07-28 11:23:01.732 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:215 -   • 目录可写: ✓
2025-07-28 11:23:01.732 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:223 -   • 可用空间: 155417.96 MB / 471482.08 MB
2025-07-28 11:23:01.733 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:239 - [INFO] Java字体系统属性:
2025-07-28 11:23:01.733 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • java.awt.fonts: 未设置
2025-07-28 11:23:01.733 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • sun.java2d.fontpath: 未设置
2025-07-28 11:23:01.733 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • java.awt.headless: true
2025-07-28 11:23:01.733 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • file.encoding: UTF-8
2025-07-28 11:23:01.733 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • sun.jnu.encoding: UTF-8
2025-07-28 11:23:01.733 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:261 - [INFO] 字体相关环境变量:
2025-07-28 11:23:01.733 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FONTCONFIG_PATH: 未设置
2025-07-28 11:23:01.733 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FONTCONFIG_FILE: 未设置
2025-07-28 11:23:01.733 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FC_DEBUG: 未设置
2025-07-28 11:23:01.733 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:280 - 检查系统字体目录:
2025-07-28 11:23:01.733 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   /usr/share/fonts - 不存在
2025-07-28 11:23:01.733 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   /usr/local/share/fonts - 不存在
2025-07-28 11:23:01.735 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:288 -   /System/Library/Fonts - 存在 (81 个字体文件)
2025-07-28 11:23:01.736 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:288 -   /Library/Fonts - 存在 (1 个字体文件)
2025-07-28 11:23:01.736 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   C:\Windows\Fonts - 不存在
2025-07-28 11:23:01.736 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:201 - ✓ [环境诊断] 环境诊断检查完成
2025-07-28 11:23:01.736 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:309 - --- 字体文件设置开始 ---
2025-07-28 11:23:01.736 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:319 - 字体资源URL: file:/Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts, 协议: file
2025-07-28 11:23:01.736 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:326 - 本地文件系统字体路径: /Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts
2025-07-28 11:23:01.736 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:622 - --- 字体文件分析开始 ---
2025-07-28 11:23:01.738 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:660 - 字体文件统计:
2025-07-28 11:23:01.738 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ini 格式: 1 个文件
2025-07-28 11:23:01.738 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .fon 格式: 192 个文件
2025-07-28 11:23:01.738 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ttc 格式: 14 个文件
2025-07-28 11:23:01.738 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .zip 格式: 1 个文件
2025-07-28 11:23:01.738 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ttf 格式: 146 个文件
2025-07-28 11:23:01.738 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .dat 格式: 1 个文件
2025-07-28 11:23:01.738 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .xml 格式: 1 个文件
2025-07-28 11:23:01.738 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:664 - 发现的优先中文字体: [Dengb.ttf, SimsunExtG.ttf, NotoSerifSC-VF.ttf, Deng.ttf, NotoSansSC-VF.ttf, HYZhongHeiTi-197.ttf, Dengl.ttf]
2025-07-28 11:23:01.738 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:665 - 发现的优先英文字体: [times.ttf, consola.ttf, arial.ttf, calibri.ttf]
2025-07-28 11:23:01.738 WARN  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:670 - 发现 192 个 .fon 格式字体文件，在Linux环境下可能不兼容
2025-07-28 11:23:01.739 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:673 - --- 字体文件分析完成 ---
2025-07-28 11:23:01.739 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:344 - --- 字体文件设置完成 ---
2025-07-28 11:23:01.739 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:680 - --- 高级字体设置开始 ---
2025-07-28 11:23:01.825 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:694 - 添加字体文件夹源: /Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts
2025-07-28 11:23:01.826 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:700 - 添加系统字体源
2025-07-28 11:23:01.835 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:718 - 配置字体替换规则...
2025-07-28 11:23:01.836 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:741 - 字体替换规则配置完成
2025-07-28 11:23:01.836 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:748 - 配置字体回退设置...
2025-07-28 11:23:01.838 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:755 - 已加载Noto字体回退设置
2025-07-28 11:23:01.838 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:763 - 字体回退设置配置完成
2025-07-28 11:23:01.838 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:711 - --- 高级字体设置完成 ---
2025-07-28 11:23:01.838 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:779 - --- 字体加载状态验证开始 ---
2025-07-28 11:23:01.838 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:785 - 配置的字体源数量: 2
2025-07-28 11:23:01.838 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:789 - 字体源 1: com.aspose.words.FolderFontSource@20a4cba7 (类型: FolderFontSource)
2025-07-28 11:23:01.838 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:793 -   字体源类型: FolderFontSource
2025-07-28 11:23:01.838 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:794 -   字体源配置完成
2025-07-28 11:23:01.838 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:789 - 字体源 2: com.aspose.words.SystemFontSource@10f10230 (类型: SystemFontSource)
2025-07-28 11:23:01.839 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:793 -   字体源类型: SystemFontSource
2025-07-28 11:23:01.839 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:794 -   字体源配置完成
2025-07-28 11:23:01.839 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:812 - 测试字体解析能力:
2025-07-28 11:23:01.839 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'SimSun' - 解析测试通过
2025-07-28 11:23:01.839 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 '宋体' - 解析测试通过
2025-07-28 11:23:01.839 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'Microsoft YaHei' - 解析测试通过
2025-07-28 11:23:01.839 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 '微软雅黑' - 解析测试通过
2025-07-28 11:23:01.839 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'Arial' - 解析测试通过
2025-07-28 11:23:01.839 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:804 - --- 字体加载状态验证完成 ---
2025-07-28 11:23:01.839 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1350 - ▶ [可用性验证] 开始字体可用性验证...
2025-07-28 11:23:01.839 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1368 - [INFO] 中文字体可用性检查:
2025-07-28 11:23:01.839 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • NotoSansSC-VF - ✓可用 (直接字体文件)
2025-07-28 11:23:01.839 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • SimSun - ✓可用 (替换为: NotoSansSC-VF.ttf)
2025-07-28 11:23:01.839 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • Microsoft YaHei - ✓可用 (替换为: HYZhongHeiTi-197.ttf)
2025-07-28 11:23:01.839 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • HYZhongHeiTi-197 - ✓可用 (直接字体文件)
2025-07-28 11:23:01.839 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1404 - [INFO] 字体替换规则验证:
2025-07-28 11:23:01.840 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1411 -   • 表格替换规则: ✓已启用
2025-07-28 11:23:01.840 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1414 -   • 字体信息替换: ✓已启用
2025-07-28 11:23:01.840 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1417 -   • 默认字体替换: ✓已启用
2025-07-28 11:23:01.840 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1420 -   • 默认字体: NotoSansSC-VF.ttf
2025-07-28 11:23:01.840 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1432 - [INFO] 字体文件完整性验证:
2025-07-28 11:23:01.840 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1435 -   • 使用本地字体目录，跳过临时文件验证
2025-07-28 11:23:01.840 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1361 - ✓ [可用性验证] 字体可用性验证完成
2025-07-28 11:23:01.840 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:164 - ============================================================
2025-07-28 11:23:01.840 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:165 - ========== 字体系统诊断完成 ==========
2025-07-28 11:23:01.840 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:166 - 完成时间: 2025-07-28 11:23:01
2025-07-28 11:23:01.840 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:167 - ============================================================
2025-07-28 11:23:01.840 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1002 - 开始docx转pdf转换: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 11:23:01.840 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:868 - ============================================================
2025-07-28 11:23:01.840 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:869 - ========== 强制字体系统诊断开始 ==========
2025-07-28 11:23:01.840 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:870 - 诊断时间: 2025-07-28 11:23:01
2025-07-28 11:23:01.840 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:871 - 字体初始化状态: 已初始化
2025-07-28 11:23:01.840 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:872 - ============================================================
2025-07-28 11:23:01.841 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:896 - ▶ [环境检查] 运行环境: 本地环境
2025-07-28 11:23:01.841 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:897 - ▶ [环境检查] 操作系统: Mac OS X
2025-07-28 11:23:01.841 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:898 - ▶ [环境检查] 工作目录: /Users/<USER>/IdeaProjects/mcp-server
2025-07-28 11:23:01.841 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:899 - ▶ [环境检查] 临时目录: /var/folders/qs/761__31103sc01c7b0m4vxth0000gp/T/
2025-07-28 11:23:01.841 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:906 - ▶ [字体文件] 开始字体文件状态检查...
2025-07-28 11:23:01.841 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:931 - [INFO] 使用本地字体目录（非JAR环境）
2025-07-28 11:23:01.841 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:939 - ▶ [字体设置] 开始字体设置验证...
2025-07-28 11:23:01.841 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:945 - [INFO] 配置的字体源数量: 2
2025-07-28 11:23:01.841 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 1: com.aspose.words.FolderFontSource@20a4cba7 (类型: FolderFontSource)
2025-07-28 11:23:01.841 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 2: com.aspose.words.SystemFontSource@10f10230 (类型: SystemFontSource)
2025-07-28 11:23:01.841 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:956 - [INFO] 字体替换规则状态:
2025-07-28 11:23:01.841 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:957 -   • 表格替换规则: ✓已启用
2025-07-28 11:23:01.841 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:958 -   • 默认字体替换: ✓已启用
2025-07-28 11:23:01.841 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:960 -   • 默认字体: NotoSansSC-VF.ttf
2025-07-28 11:23:01.842 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1368 - [INFO] 中文字体可用性检查:
2025-07-28 11:23:01.842 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • NotoSansSC-VF - ✓可用 (直接字体文件)
2025-07-28 11:23:01.842 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • SimSun - ✓可用 (替换为: NotoSansSC-VF.ttf)
2025-07-28 11:23:01.842 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • Microsoft YaHei - ✓可用 (替换为: HYZhongHeiTi-197.ttf)
2025-07-28 11:23:01.842 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • HYZhongHeiTi-197 - ✓可用 (直接字体文件)
2025-07-28 11:23:01.842 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:886 - ============================================================
2025-07-28 11:23:01.842 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:887 - ========== 强制字体系统诊断完成 ==========
2025-07-28 11:23:01.842 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:888 - 完成时间: 2025-07-28 11:23:01
2025-07-28 11:23:01.842 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:889 - ============================================================
2025-07-28 11:23:01.842 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1008 - ▶ [文档加载] 开始加载文档: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 11:23:02.770 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1010 - ✓ [文档加载] 文档加载成功，开始应用字体设置
2025-07-28 11:23:02.770 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1014 - ✓ [字体设置] 全局字体设置已应用到文档
2025-07-28 11:23:02.770 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1063 - ▶ [字体分析] 开始分析文档字体使用情况: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 11:23:02.770 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1070 - [INFO] 文档字体源配置: 2 个字体源
2025-07-28 11:23:02.770 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1074 -   字体源 1: FolderFontSource
2025-07-28 11:23:02.770 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1074 -   字体源 2: SystemFontSource
2025-07-28 11:23:02.824 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1104 - [INFO] 文档中使用的字体: 宋体, 仿宋, Times New Roman, Wingdings 2
2025-07-28 11:23:02.825 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1112 - [INFO] 包含中文字体: 是
2025-07-28 11:23:02.825 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1081 - ✓ [字体分析] 文档字体使用情况分析完成
2025-07-28 11:23:02.825 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1021 - ▶ [PDF转换] 开始PDF转换，应用字体嵌入配置...
2025-07-28 11:23:02.825 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1126 - ▶ [PDF字体] 验证PDF字体嵌入设置...
2025-07-28 11:23:02.826 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1135 - [INFO] PDF字体嵌入配置:
2025-07-28 11:23:02.826 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1136 -   • 嵌入完整字体: ✓已启用
2025-07-28 11:23:02.826 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1137 -   • 字体嵌入模式: EMBED_ALL (嵌入所有字体)
2025-07-28 11:23:02.826 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1138 -   • 文本压缩: FLATE
2025-07-28 11:23:02.826 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1025 - ▶ [转换执行] 开始执行docx转pdf转换...
2025-07-28 11:23:09.315 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1027 - ✓ [转换执行] docx转pdf转换执行完成
2025-07-28 11:23:09.319 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1035 - ✓ [转换完成] docx转pdf转换成功完成:
2025-07-28 11:23:09.319 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1036 -   • 源文件: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx (输入大小: 14817280 bytes)
2025-07-28 11:23:09.319 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1037 -   • 目标文件: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.pdf (输出大小: 26627709 bytes)
2025-07-28 11:23:09.320 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1038 -   • 压缩比: 179.71%
2025-07-28 11:23:09.322 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1149 - ▶ [PDF验证] 验证PDF字体嵌入结果...
2025-07-28 11:23:09.322 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1152 - [INFO] PDF文件生成统计:
2025-07-28 11:23:09.322 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1153 -   • PDF文件大小: 26003 KB
2025-07-28 11:23:09.322 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1159 -   • 包含字体信息: ✓是
2025-07-28 11:23:09.322 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:63 - ✓ 终极质量转换完成！
2025-07-28 11:23:09.323 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:64 - === 转换统计信息 ===
2025-07-28 11:23:09.323 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:65 -   - 转换耗时: 7591 ms ({:.2f} 秒)
2025-07-28 11:23:09.323 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:66 -   - 原文件大小: 14817280 bytes (14470 KB)
2025-07-28 11:23:09.323 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:67 -   - PDF文件大小: 26627709 bytes (26003 KB)
2025-07-28 11:23:09.323 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:68 -   - 文件大小比率: {:.2f}%
2025-07-28 11:23:09.323 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:69 -   - 转换速度: {:.2f} KB/s
2025-07-28 11:23:09.332 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:81 - ✓ 终极质量PDF文件已保存到: target/test-output/ultimate_quality_20250728_112309.pdf
2025-07-28 11:23:09.332 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:207 - === 执行详细验证 ===
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:219 - ✓ PDF格式验证通过
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:226 - ✓ 文件大小验证通过: 26627709 bytes
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:231 - ✓ 文件可读性验证通过
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:233 - === 详细验证完成 ===
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:87 - === 终极质量转换测试完成 ===
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:88 - 请检查输出文件: target/test-output/ultimate_quality_20250728_112309.pdf 以验证格式保持效果
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:292 - 
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:293 - === 终极质量PDF检查清单 ===
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:294 - 请手动验证以下格式保持情况（深度优化版）:
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:295 - 
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:296 - 【字体质量检查】
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:297 - □ 1. 中文字体完美显示（宋体、黑体、微软雅黑、楷体、仿宋）
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:298 - □ 2. 英文字体完美显示（Times New Roman、Arial、Calibri、Cambria）
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:299 - □ 3. 字体大小精确保持，无缩放变形
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:300 - □ 4. 字体粗细（加粗、正常）完全一致
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:301 - □ 5. 字体样式（斜体、下划线）完全保持
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:302 - 
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:303 - 【格式质量检查】
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:304 - □ 6. 文字颜色和背景色精确还原
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:305 - □ 7. 段落间距和行距完全一致
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:306 - □ 8. 文本对齐方式（左对齐、居中、右对齐、两端对齐）保持
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:307 - □ 9. 首行缩进和段落缩进精确保持
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:308 - □ 10. 项目符号和编号格式完整
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:309 - 
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:310 - 【表格质量检查】
2025-07-28 11:23:09.333 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:311 - □ 11. 表格边框线条粗细和颜色正确
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:312 - □ 12. 单元格背景色和文字颜色保持
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:313 - □ 13. 表格列宽和行高精确保持
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:314 - □ 14. 单元格内文字对齐方式正确
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:315 - □ 15. 合并单元格格式完整
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:316 - 
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:317 - 【页面布局检查】
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:318 - □ 16. 页面边距完全一致
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:319 - □ 17. 页眉页脚内容和格式完整
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:320 - □ 18. 页码位置和格式正确
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:321 - □ 19. 分页位置准确，无异常分页
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:322 - □ 20. 页面方向（横向/纵向）保持
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:323 - 
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:324 - 【图像质量检查】
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:325 - □ 21. 图片清晰度高，无模糊
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:326 - □ 22. 图片位置和大小精确
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:327 - □ 23. 图片环绕文字方式保持
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:328 - □ 24. 图片颜色和对比度正确
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:329 - 
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:330 - 【功能性检查】
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:331 - □ 25. 超链接可点击且跳转正确
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:332 - □ 26. 书签和目录结构完整
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:333 - □ 27. 交叉引用功能正常
2025-07-28 11:23:09.334 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:334 - □ 28. 脚注和尾注格式正确
2025-07-28 11:23:09.335 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:335 - 
2025-07-28 11:23:09.335 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:336 - 【整体质量检查】
2025-07-28 11:23:09.335 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:337 - □ 29. 整体视觉效果与Word文档100%一致
2025-07-28 11:23:09.335 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:338 - □ 30. 无任何格式丢失或变形
2025-07-28 11:23:09.335 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:339 - □ 31. 文档结构完整，无缺失内容
2025-07-28 11:23:09.335 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:340 - □ 32. PDF文件大小合理，质量优秀
2025-07-28 11:23:09.335 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:341 - 
2025-07-28 11:23:09.335 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:342 - 如果以上所有项目都符合预期，说明终极质量格式保持修复成功！
2025-07-28 11:23:09.335 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:343 - === 检查清单结束 ===
2025-07-28 11:34:48.112 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 11:34:48.116 INFO  o.s.b.w.e.n.GracefulShutdown:62 - Graceful shutdown complete
2025-07-28 11:34:58.053 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-07-28 11:34:58.084 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 12164 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 11:34:58.084 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 11:34:58.381 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=7aa4dde4-72ab-3979-827f-45817dc1e4e8
2025-07-28 11:34:58.816 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 11:34:58.824 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.981 seconds (process running for 2.009)
2025-07-28 11:35:49.323 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 11:35:49.328 INFO  o.s.b.w.e.n.GracefulShutdown:62 - Graceful shutdown complete
2025-07-28 11:35:52.547 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-07-28 11:35:52.573 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 12246 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 11:35:52.574 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 11:35:52.858 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=7aa4dde4-72ab-3979-827f-45817dc1e4e8
2025-07-28 11:35:53.303 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 11:35:53.311 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.965 seconds (process running for 1.436)
2025-07-28 11:36:25.757 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:153 - ============================================================
2025-07-28 11:36:25.758 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:154 - ========== 字体系统诊断开始 ==========
2025-07-28 11:36:25.760 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:155 - 诊断时间: 2025-07-28 11:36:25
2025-07-28 11:36:25.760 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:156 - ============================================================
2025-07-28 11:36:25.760 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:174 - ▶ [环境诊断] 开始环境诊断检查...
2025-07-28 11:36:25.761 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:183 - [INFO] 运行环境信息:
2025-07-28 11:36:25.761 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:184 -   • 操作系统: Mac OS X 15.5
2025-07-28 11:36:25.761 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:185 -   • Java版本: 17.0.15 (Microsoft)
2025-07-28 11:36:25.761 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:186 -   • 工作目录: /Users/<USER>/IdeaProjects/mcp-server
2025-07-28 11:36:25.761 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:190 -   • 运行环境: 本地环境
2025-07-28 11:36:25.761 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:211 - [INFO] 临时目录状态:
2025-07-28 11:36:25.761 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:212 -   • 临时目录路径: /var/folders/qs/761__31103sc01c7b0m4vxth0000gp/T/
2025-07-28 11:36:25.761 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:213 -   • 目录存在: ✓
2025-07-28 11:36:25.761 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:214 -   • 目录可读: ✓
2025-07-28 11:36:25.761 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:215 -   • 目录可写: ✓
2025-07-28 11:36:25.762 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:223 -   • 可用空间: 156038.99 MB / 471482.08 MB
2025-07-28 11:36:25.762 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:239 - [INFO] Java字体系统属性:
2025-07-28 11:36:25.762 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • java.awt.fonts: 未设置
2025-07-28 11:36:25.762 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • sun.java2d.fontpath: 未设置
2025-07-28 11:36:25.762 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • java.awt.headless: true
2025-07-28 11:36:25.762 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • file.encoding: UTF-8
2025-07-28 11:36:25.762 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • sun.jnu.encoding: UTF-8
2025-07-28 11:36:25.762 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:261 - [INFO] 字体相关环境变量:
2025-07-28 11:36:25.762 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FONTCONFIG_PATH: 未设置
2025-07-28 11:36:25.763 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FONTCONFIG_FILE: 未设置
2025-07-28 11:36:25.763 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FC_DEBUG: 未设置
2025-07-28 11:36:25.763 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:280 - 检查系统字体目录:
2025-07-28 11:36:25.763 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   /usr/share/fonts - 不存在
2025-07-28 11:36:25.763 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   /usr/local/share/fonts - 不存在
2025-07-28 11:36:25.764 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:288 -   /System/Library/Fonts - 存在 (81 个字体文件)
2025-07-28 11:36:25.765 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:288 -   /Library/Fonts - 存在 (1 个字体文件)
2025-07-28 11:36:25.765 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   C:\Windows\Fonts - 不存在
2025-07-28 11:36:25.765 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:201 - ✓ [环境诊断] 环境诊断检查完成
2025-07-28 11:36:25.765 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:309 - --- 字体文件设置开始 ---
2025-07-28 11:36:25.765 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:319 - 字体资源URL: file:/Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts, 协议: file
2025-07-28 11:36:25.765 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:326 - 本地文件系统字体路径: /Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts
2025-07-28 11:36:25.765 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:622 - --- 字体文件分析开始 ---
2025-07-28 11:36:25.768 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:660 - 字体文件统计:
2025-07-28 11:36:25.768 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ini 格式: 1 个文件
2025-07-28 11:36:25.768 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .fon 格式: 192 个文件
2025-07-28 11:36:25.768 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ttc 格式: 14 个文件
2025-07-28 11:36:25.768 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .zip 格式: 1 个文件
2025-07-28 11:36:25.768 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ttf 格式: 146 个文件
2025-07-28 11:36:25.768 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .dat 格式: 1 个文件
2025-07-28 11:36:25.768 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .xml 格式: 1 个文件
2025-07-28 11:36:25.768 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:664 - 发现的优先中文字体: [Dengb.ttf, SimsunExtG.ttf, NotoSerifSC-VF.ttf, Deng.ttf, NotoSansSC-VF.ttf, HYZhongHeiTi-197.ttf, Dengl.ttf]
2025-07-28 11:36:25.768 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:665 - 发现的优先英文字体: [times.ttf, consola.ttf, arial.ttf, calibri.ttf]
2025-07-28 11:36:25.768 WARN  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:670 - 发现 192 个 .fon 格式字体文件，在Linux环境下可能不兼容
2025-07-28 11:36:25.768 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:673 - --- 字体文件分析完成 ---
2025-07-28 11:36:25.769 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:344 - --- 字体文件设置完成 ---
2025-07-28 11:36:25.769 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:680 - --- 高级字体设置开始 ---
2025-07-28 11:36:25.859 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:694 - 添加字体文件夹源: /Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts
2025-07-28 11:36:25.859 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:700 - 添加系统字体源
2025-07-28 11:36:25.866 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:718 - 配置字体替换规则...
2025-07-28 11:36:25.866 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:741 - 字体替换规则配置完成
2025-07-28 11:36:25.866 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:748 - 配置字体回退设置...
2025-07-28 11:36:25.867 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:755 - 已加载Noto字体回退设置
2025-07-28 11:36:25.867 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:763 - 字体回退设置配置完成
2025-07-28 11:36:25.867 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:711 - --- 高级字体设置完成 ---
2025-07-28 11:36:25.867 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:779 - --- 字体加载状态验证开始 ---
2025-07-28 11:36:25.867 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:785 - 配置的字体源数量: 2
2025-07-28 11:36:25.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:789 - 字体源 1: com.aspose.words.FolderFontSource@2b5fd2b3 (类型: FolderFontSource)
2025-07-28 11:36:25.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:793 -   字体源类型: FolderFontSource
2025-07-28 11:36:25.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:794 -   字体源配置完成
2025-07-28 11:36:25.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:789 - 字体源 2: com.aspose.words.SystemFontSource@4734c41 (类型: SystemFontSource)
2025-07-28 11:36:25.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:793 -   字体源类型: SystemFontSource
2025-07-28 11:36:25.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:794 -   字体源配置完成
2025-07-28 11:36:25.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:812 - 测试字体解析能力:
2025-07-28 11:36:25.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'SimSun' - 解析测试通过
2025-07-28 11:36:25.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 '宋体' - 解析测试通过
2025-07-28 11:36:25.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'Microsoft YaHei' - 解析测试通过
2025-07-28 11:36:25.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 '微软雅黑' - 解析测试通过
2025-07-28 11:36:25.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'Arial' - 解析测试通过
2025-07-28 11:36:25.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:804 - --- 字体加载状态验证完成 ---
2025-07-28 11:36:25.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1350 - ▶ [可用性验证] 开始字体可用性验证...
2025-07-28 11:36:25.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1368 - [INFO] 中文字体可用性检查:
2025-07-28 11:36:25.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • NotoSansSC-VF - ✓可用 (直接字体文件)
2025-07-28 11:36:25.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • SimSun - ✓可用 (替换为: NotoSansSC-VF.ttf)
2025-07-28 11:36:25.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • Microsoft YaHei - ✓可用 (替换为: HYZhongHeiTi-197.ttf)
2025-07-28 11:36:25.869 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • HYZhongHeiTi-197 - ✓可用 (直接字体文件)
2025-07-28 11:36:25.869 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1404 - [INFO] 字体替换规则验证:
2025-07-28 11:36:25.869 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1411 -   • 表格替换规则: ✓已启用
2025-07-28 11:36:25.869 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1414 -   • 字体信息替换: ✓已启用
2025-07-28 11:36:25.869 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1417 -   • 默认字体替换: ✓已启用
2025-07-28 11:36:25.869 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1420 -   • 默认字体: NotoSansSC-VF.ttf
2025-07-28 11:36:25.869 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1432 - [INFO] 字体文件完整性验证:
2025-07-28 11:36:25.869 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1435 -   • 使用本地字体目录，跳过临时文件验证
2025-07-28 11:36:25.869 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1361 - ✓ [可用性验证] 字体可用性验证完成
2025-07-28 11:36:25.869 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:164 - ============================================================
2025-07-28 11:36:25.869 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:165 - ========== 字体系统诊断完成 ==========
2025-07-28 11:36:25.869 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:166 - 完成时间: 2025-07-28 11:36:25
2025-07-28 11:36:25.869 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:167 - ============================================================
2025-07-28 11:36:25.869 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1002 - 开始docx转pdf转换: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 11:36:25.869 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:868 - ============================================================
2025-07-28 11:36:25.870 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:869 - ========== 强制字体系统诊断开始 ==========
2025-07-28 11:36:25.870 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:870 - 诊断时间: 2025-07-28 11:36:25
2025-07-28 11:36:25.870 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:871 - 字体初始化状态: 已初始化
2025-07-28 11:36:25.870 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:872 - ============================================================
2025-07-28 11:36:25.870 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:896 - ▶ [环境检查] 运行环境: 本地环境
2025-07-28 11:36:25.870 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:897 - ▶ [环境检查] 操作系统: Mac OS X
2025-07-28 11:36:25.870 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:898 - ▶ [环境检查] 工作目录: /Users/<USER>/IdeaProjects/mcp-server
2025-07-28 11:36:25.870 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:899 - ▶ [环境检查] 临时目录: /var/folders/qs/761__31103sc01c7b0m4vxth0000gp/T/
2025-07-28 11:36:25.870 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:906 - ▶ [字体文件] 开始字体文件状态检查...
2025-07-28 11:36:25.870 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:931 - [INFO] 使用本地字体目录（非JAR环境）
2025-07-28 11:36:25.870 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:939 - ▶ [字体设置] 开始字体设置验证...
2025-07-28 11:36:25.870 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:945 - [INFO] 配置的字体源数量: 2
2025-07-28 11:36:25.870 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 1: com.aspose.words.FolderFontSource@2b5fd2b3 (类型: FolderFontSource)
2025-07-28 11:36:25.870 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 2: com.aspose.words.SystemFontSource@4734c41 (类型: SystemFontSource)
2025-07-28 11:36:25.870 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:956 - [INFO] 字体替换规则状态:
2025-07-28 11:36:25.870 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:957 -   • 表格替换规则: ✓已启用
2025-07-28 11:36:25.870 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:958 -   • 默认字体替换: ✓已启用
2025-07-28 11:36:25.871 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:960 -   • 默认字体: NotoSansSC-VF.ttf
2025-07-28 11:36:25.871 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1368 - [INFO] 中文字体可用性检查:
2025-07-28 11:36:25.871 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • NotoSansSC-VF - ✓可用 (直接字体文件)
2025-07-28 11:36:25.871 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • SimSun - ✓可用 (替换为: NotoSansSC-VF.ttf)
2025-07-28 11:36:25.871 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • Microsoft YaHei - ✓可用 (替换为: HYZhongHeiTi-197.ttf)
2025-07-28 11:36:25.871 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • HYZhongHeiTi-197 - ✓可用 (直接字体文件)
2025-07-28 11:36:25.871 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:886 - ============================================================
2025-07-28 11:36:25.871 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:887 - ========== 强制字体系统诊断完成 ==========
2025-07-28 11:36:25.871 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:888 - 完成时间: 2025-07-28 11:36:25
2025-07-28 11:36:25.871 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:889 - ============================================================
2025-07-28 11:36:25.871 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1008 - ▶ [文档加载] 开始加载文档: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 11:36:26.757 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1010 - ✓ [文档加载] 文档加载成功，开始应用字体设置
2025-07-28 11:36:26.758 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1014 - ✓ [字体设置] 全局字体设置已应用到文档
2025-07-28 11:36:26.758 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1063 - ▶ [字体分析] 开始分析文档字体使用情况: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 11:36:26.758 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1070 - [INFO] 文档字体源配置: 2 个字体源
2025-07-28 11:36:26.758 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1074 -   字体源 1: FolderFontSource
2025-07-28 11:36:26.758 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1074 -   字体源 2: SystemFontSource
2025-07-28 11:36:26.813 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1104 - [INFO] 文档中使用的字体: 宋体, 仿宋, Times New Roman, Wingdings 2
2025-07-28 11:36:26.814 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1112 - [INFO] 包含中文字体: 是
2025-07-28 11:36:26.814 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1081 - ✓ [字体分析] 文档字体使用情况分析完成
2025-07-28 11:36:26.814 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1021 - ▶ [PDF转换] 开始PDF转换，应用字体嵌入配置...
2025-07-28 11:36:26.814 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1126 - ▶ [PDF字体] 验证PDF字体嵌入设置...
2025-07-28 11:36:26.816 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1135 - [INFO] PDF字体嵌入配置:
2025-07-28 11:36:26.816 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1136 -   • 嵌入完整字体: ✓已启用
2025-07-28 11:36:26.816 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1137 -   • 字体嵌入模式: EMBED_ALL (嵌入所有字体)
2025-07-28 11:36:26.816 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1138 -   • 文本压缩: FLATE
2025-07-28 11:36:26.816 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1025 - ▶ [转换执行] 开始执行docx转pdf转换...
2025-07-28 11:36:34.821 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1027 - ✓ [转换执行] docx转pdf转换执行完成
2025-07-28 11:36:34.825 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1035 - ✓ [转换完成] docx转pdf转换成功完成:
2025-07-28 11:36:34.825 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1036 -   • 源文件: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx (输入大小: 14817280 bytes)
2025-07-28 11:36:34.825 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1037 -   • 目标文件: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.pdf (输出大小: 26627709 bytes)
2025-07-28 11:36:34.826 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1038 -   • 压缩比: 179.71%
2025-07-28 11:36:34.828 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1149 - ▶ [PDF验证] 验证PDF字体嵌入结果...
2025-07-28 11:36:34.828 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1152 - [INFO] PDF文件生成统计:
2025-07-28 11:36:34.828 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1153 -   • PDF文件大小: 26003 KB
2025-07-28 11:36:34.828 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1159 -   • 包含字体信息: ✓是
2025-07-28 11:36:34.828 INFO  c.j.j.j.a.m.s.i.AsposeConvertRuleImpl:86 - 规则引擎执行成功：.docx -> .pdf
2025-07-28 12:17:53.333 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 12:19:18.941 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-28 12:19:18.973 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 16647 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 12:19:18.974 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 12:19:19.324 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=dc45cc65-8b60-3b9e-a5fd-0ca3aa608d29
2025-07-28 12:19:19.776 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 12:19:19.788 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.112 seconds (process running for 2.082)
2025-07-28 12:20:18.721 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:153 - ============================================================
2025-07-28 12:20:18.722 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:154 - ========== 字体系统诊断开始 ==========
2025-07-28 12:20:18.725 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:155 - 诊断时间: 2025-07-28 12:20:18
2025-07-28 12:20:18.725 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:156 - ============================================================
2025-07-28 12:20:18.725 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:174 - ▶ [环境诊断] 开始环境诊断检查...
2025-07-28 12:20:18.725 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:183 - [INFO] 运行环境信息:
2025-07-28 12:20:18.725 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:184 -   • 操作系统: Mac OS X 15.5
2025-07-28 12:20:18.725 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:185 -   • Java版本: 17.0.15 (Microsoft)
2025-07-28 12:20:18.725 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:186 -   • 工作目录: /Users/<USER>/IdeaProjects/mcp-server
2025-07-28 12:20:18.726 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:190 -   • 运行环境: 本地环境
2025-07-28 12:20:18.726 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:211 - [INFO] 临时目录状态:
2025-07-28 12:20:18.726 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:212 -   • 临时目录路径: /var/folders/qs/761__31103sc01c7b0m4vxth0000gp/T/
2025-07-28 12:20:18.726 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:213 -   • 目录存在: ✓
2025-07-28 12:20:18.726 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:214 -   • 目录可读: ✓
2025-07-28 12:20:18.726 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:215 -   • 目录可写: ✓
2025-07-28 12:20:18.727 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:223 -   • 可用空间: 154505.14 MB / 471482.08 MB
2025-07-28 12:20:18.727 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:239 - [INFO] Java字体系统属性:
2025-07-28 12:20:18.727 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • java.awt.fonts: 未设置
2025-07-28 12:20:18.727 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • sun.java2d.fontpath: 未设置
2025-07-28 12:20:18.727 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • java.awt.headless: true
2025-07-28 12:20:18.727 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • file.encoding: UTF-8
2025-07-28 12:20:18.728 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • sun.jnu.encoding: UTF-8
2025-07-28 12:20:18.728 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:261 - [INFO] 字体相关环境变量:
2025-07-28 12:20:18.728 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FONTCONFIG_PATH: 未设置
2025-07-28 12:20:18.728 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FONTCONFIG_FILE: 未设置
2025-07-28 12:20:18.728 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FC_DEBUG: 未设置
2025-07-28 12:20:18.728 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:280 - 检查系统字体目录:
2025-07-28 12:20:18.728 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   /usr/share/fonts - 不存在
2025-07-28 12:20:18.729 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   /usr/local/share/fonts - 不存在
2025-07-28 12:20:18.730 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:288 -   /System/Library/Fonts - 存在 (81 个字体文件)
2025-07-28 12:20:18.731 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:288 -   /Library/Fonts - 存在 (1 个字体文件)
2025-07-28 12:20:18.731 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   C:\Windows\Fonts - 不存在
2025-07-28 12:20:18.731 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:201 - ✓ [环境诊断] 环境诊断检查完成
2025-07-28 12:20:18.731 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:309 - --- 字体文件设置开始 ---
2025-07-28 12:20:18.731 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:319 - 字体资源URL: file:/Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts, 协议: file
2025-07-28 12:20:18.732 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:326 - 本地文件系统字体路径: /Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts
2025-07-28 12:20:18.732 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:622 - --- 字体文件分析开始 ---
2025-07-28 12:20:18.735 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:660 - 字体文件统计:
2025-07-28 12:20:18.735 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ini 格式: 1 个文件
2025-07-28 12:20:18.735 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .fon 格式: 192 个文件
2025-07-28 12:20:18.735 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ttc 格式: 14 个文件
2025-07-28 12:20:18.735 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .zip 格式: 1 个文件
2025-07-28 12:20:18.735 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ttf 格式: 146 个文件
2025-07-28 12:20:18.735 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .dat 格式: 1 个文件
2025-07-28 12:20:18.735 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .xml 格式: 1 个文件
2025-07-28 12:20:18.735 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:664 - 发现的优先中文字体: [Dengb.ttf, SimsunExtG.ttf, NotoSerifSC-VF.ttf, Deng.ttf, NotoSansSC-VF.ttf, HYZhongHeiTi-197.ttf, Dengl.ttf]
2025-07-28 12:20:18.736 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:665 - 发现的优先英文字体: [times.ttf, consola.ttf, arial.ttf, calibri.ttf]
2025-07-28 12:20:18.736 WARN  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:670 - 发现 192 个 .fon 格式字体文件，在Linux环境下可能不兼容
2025-07-28 12:20:18.736 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:673 - --- 字体文件分析完成 ---
2025-07-28 12:20:18.736 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:344 - --- 字体文件设置完成 ---
2025-07-28 12:20:18.736 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:680 - --- 高级字体设置开始 ---
2025-07-28 12:20:18.840 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:694 - 添加字体文件夹源: /Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts
2025-07-28 12:20:18.840 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:700 - 添加系统字体源
2025-07-28 12:20:18.849 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:718 - 配置字体替换规则...
2025-07-28 12:20:18.849 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:741 - 字体替换规则配置完成
2025-07-28 12:20:18.849 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:748 - 配置字体回退设置...
2025-07-28 12:20:18.851 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:755 - 已加载Noto字体回退设置
2025-07-28 12:20:18.851 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:763 - 字体回退设置配置完成
2025-07-28 12:20:18.851 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:711 - --- 高级字体设置完成 ---
2025-07-28 12:20:18.851 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:779 - --- 字体加载状态验证开始 ---
2025-07-28 12:20:18.851 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:785 - 配置的字体源数量: 2
2025-07-28 12:20:18.851 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:789 - 字体源 1: com.aspose.words.FolderFontSource@e54eae3 (类型: FolderFontSource)
2025-07-28 12:20:18.851 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:793 -   字体源类型: FolderFontSource
2025-07-28 12:20:18.851 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:794 -   字体源配置完成
2025-07-28 12:20:18.852 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:789 - 字体源 2: com.aspose.words.SystemFontSource@19251f39 (类型: SystemFontSource)
2025-07-28 12:20:18.852 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:793 -   字体源类型: SystemFontSource
2025-07-28 12:20:18.852 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:794 -   字体源配置完成
2025-07-28 12:20:18.852 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:812 - 测试字体解析能力:
2025-07-28 12:20:18.852 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'SimSun' - 解析测试通过
2025-07-28 12:20:18.852 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 '宋体' - 解析测试通过
2025-07-28 12:20:18.852 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'Microsoft YaHei' - 解析测试通过
2025-07-28 12:20:18.852 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 '微软雅黑' - 解析测试通过
2025-07-28 12:20:18.852 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'Arial' - 解析测试通过
2025-07-28 12:20:18.852 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:804 - --- 字体加载状态验证完成 ---
2025-07-28 12:20:18.852 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1350 - ▶ [可用性验证] 开始字体可用性验证...
2025-07-28 12:20:18.852 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1368 - [INFO] 中文字体可用性检查:
2025-07-28 12:20:18.852 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • NotoSansSC-VF - ✓可用 (直接字体文件)
2025-07-28 12:20:18.852 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • SimSun - ✓可用 (替换为: NotoSansSC-VF.ttf)
2025-07-28 12:20:18.852 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • Microsoft YaHei - ✓可用 (替换为: HYZhongHeiTi-197.ttf)
2025-07-28 12:20:18.852 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • HYZhongHeiTi-197 - ✓可用 (直接字体文件)
2025-07-28 12:20:18.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1404 - [INFO] 字体替换规则验证:
2025-07-28 12:20:18.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1411 -   • 表格替换规则: ✓已启用
2025-07-28 12:20:18.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1414 -   • 字体信息替换: ✓已启用
2025-07-28 12:20:18.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1417 -   • 默认字体替换: ✓已启用
2025-07-28 12:20:18.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1420 -   • 默认字体: NotoSansSC-VF.ttf
2025-07-28 12:20:18.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1432 - [INFO] 字体文件完整性验证:
2025-07-28 12:20:18.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1435 -   • 使用本地字体目录，跳过临时文件验证
2025-07-28 12:20:18.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1361 - ✓ [可用性验证] 字体可用性验证完成
2025-07-28 12:20:18.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:164 - ============================================================
2025-07-28 12:20:18.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:165 - ========== 字体系统诊断完成 ==========
2025-07-28 12:20:18.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:166 - 完成时间: 2025-07-28 12:20:18
2025-07-28 12:20:18.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:167 - ============================================================
2025-07-28 12:20:18.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1002 - 开始docx转pdf转换: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 12:20:18.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:868 - ============================================================
2025-07-28 12:20:18.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:869 - ========== 强制字体系统诊断开始 ==========
2025-07-28 12:20:18.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:870 - 诊断时间: 2025-07-28 12:20:18
2025-07-28 12:20:18.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:871 - 字体初始化状态: 已初始化
2025-07-28 12:20:18.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:872 - ============================================================
2025-07-28 12:20:18.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:896 - ▶ [环境检查] 运行环境: 本地环境
2025-07-28 12:20:18.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:897 - ▶ [环境检查] 操作系统: Mac OS X
2025-07-28 12:20:18.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:898 - ▶ [环境检查] 工作目录: /Users/<USER>/IdeaProjects/mcp-server
2025-07-28 12:20:18.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:899 - ▶ [环境检查] 临时目录: /var/folders/qs/761__31103sc01c7b0m4vxth0000gp/T/
2025-07-28 12:20:18.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:906 - ▶ [字体文件] 开始字体文件状态检查...
2025-07-28 12:20:18.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:931 - [INFO] 使用本地字体目录（非JAR环境）
2025-07-28 12:20:18.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:939 - ▶ [字体设置] 开始字体设置验证...
2025-07-28 12:20:18.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:945 - [INFO] 配置的字体源数量: 2
2025-07-28 12:20:18.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 1: com.aspose.words.FolderFontSource@e54eae3 (类型: FolderFontSource)
2025-07-28 12:20:18.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 2: com.aspose.words.SystemFontSource@19251f39 (类型: SystemFontSource)
2025-07-28 12:20:18.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:956 - [INFO] 字体替换规则状态:
2025-07-28 12:20:18.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:957 -   • 表格替换规则: ✓已启用
2025-07-28 12:20:18.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:958 -   • 默认字体替换: ✓已启用
2025-07-28 12:20:18.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:960 -   • 默认字体: NotoSansSC-VF.ttf
2025-07-28 12:20:18.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1368 - [INFO] 中文字体可用性检查:
2025-07-28 12:20:18.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • NotoSansSC-VF - ✓可用 (直接字体文件)
2025-07-28 12:20:18.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • SimSun - ✓可用 (替换为: NotoSansSC-VF.ttf)
2025-07-28 12:20:18.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • Microsoft YaHei - ✓可用 (替换为: HYZhongHeiTi-197.ttf)
2025-07-28 12:20:18.855 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • HYZhongHeiTi-197 - ✓可用 (直接字体文件)
2025-07-28 12:20:18.855 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:886 - ============================================================
2025-07-28 12:20:18.855 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:887 - ========== 强制字体系统诊断完成 ==========
2025-07-28 12:20:18.855 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:888 - 完成时间: 2025-07-28 12:20:18
2025-07-28 12:20:18.855 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:889 - ============================================================
2025-07-28 12:20:18.855 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1008 - ▶ [文档加载] 开始加载文档: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 12:20:19.802 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1010 - ✓ [文档加载] 文档加载成功，开始应用字体设置
2025-07-28 12:20:19.802 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1014 - ✓ [字体设置] 全局字体设置已应用到文档
2025-07-28 12:20:19.802 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1063 - ▶ [字体分析] 开始分析文档字体使用情况: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 12:20:19.802 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1070 - [INFO] 文档字体源配置: 2 个字体源
2025-07-28 12:20:19.802 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1074 -   字体源 1: FolderFontSource
2025-07-28 12:20:19.802 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1074 -   字体源 2: SystemFontSource
2025-07-28 12:20:19.865 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1104 - [INFO] 文档中使用的字体: 宋体, 仿宋, Times New Roman, Wingdings 2
2025-07-28 12:20:19.865 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1112 - [INFO] 包含中文字体: 是
2025-07-28 12:20:19.865 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1081 - ✓ [字体分析] 文档字体使用情况分析完成
2025-07-28 12:20:19.865 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1021 - ▶ [PDF转换] 开始PDF转换，应用字体嵌入配置...
2025-07-28 12:20:19.865 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1126 - ▶ [PDF字体] 验证PDF字体嵌入设置...
2025-07-28 12:20:19.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1135 - [INFO] PDF字体嵌入配置:
2025-07-28 12:20:19.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1136 -   • 嵌入完整字体: ✓已启用
2025-07-28 12:20:19.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1137 -   • 字体嵌入模式: EMBED_ALL (嵌入所有字体)
2025-07-28 12:20:19.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1138 -   • 文本压缩: FLATE
2025-07-28 12:20:19.868 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1025 - ▶ [转换执行] 开始执行docx转pdf转换...
2025-07-28 12:20:27.233 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1027 - ✓ [转换执行] docx转pdf转换执行完成
2025-07-28 12:20:27.236 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1035 - ✓ [转换完成] docx转pdf转换成功完成:
2025-07-28 12:20:27.236 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1036 -   • 源文件: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx (输入大小: 14817280 bytes)
2025-07-28 12:20:27.236 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1037 -   • 目标文件: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.pdf (输出大小: 26627709 bytes)
2025-07-28 12:20:27.237 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1038 -   • 压缩比: 179.71%
2025-07-28 12:20:27.244 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1149 - ▶ [PDF验证] 验证PDF字体嵌入结果...
2025-07-28 12:20:27.245 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1152 - [INFO] PDF文件生成统计:
2025-07-28 12:20:27.245 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1153 -   • PDF文件大小: 26003 KB
2025-07-28 12:20:27.245 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1159 -   • 包含字体信息: ✓是
2025-07-28 12:20:27.245 INFO  c.j.j.j.a.m.s.i.AsposeConvertRuleImpl:86 - 规则引擎执行成功：.docx -> .pdf
2025-07-28 12:42:24.485 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 13:19:25.051 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-28 13:19:25.086 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 22486 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 13:19:25.086 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 13:19:25.454 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=dc45cc65-8b60-3b9e-a5fd-0ca3aa608d29
2025-07-28 13:19:25.925 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 13:19:25.935 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.158 seconds (process running for 2.238)
2025-07-28 13:20:33.847 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:153 - ============================================================
2025-07-28 13:20:33.850 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:154 - ========== 字体系统诊断开始 ==========
2025-07-28 13:20:33.852 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:155 - 诊断时间: 2025-07-28 13:20:33
2025-07-28 13:20:33.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:156 - ============================================================
2025-07-28 13:20:33.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:174 - ▶ [环境诊断] 开始环境诊断检查...
2025-07-28 13:20:33.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:183 - [INFO] 运行环境信息:
2025-07-28 13:20:33.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:184 -   • 操作系统: Mac OS X 15.5
2025-07-28 13:20:33.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:185 -   • Java版本: 17.0.15 (Microsoft)
2025-07-28 13:20:33.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:186 -   • 工作目录: /Users/<USER>/IdeaProjects/mcp-server
2025-07-28 13:20:33.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:190 -   • 运行环境: 本地环境
2025-07-28 13:20:33.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:211 - [INFO] 临时目录状态:
2025-07-28 13:20:33.853 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:212 -   • 临时目录路径: /var/folders/qs/761__31103sc01c7b0m4vxth0000gp/T/
2025-07-28 13:20:33.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:213 -   • 目录存在: ✓
2025-07-28 13:20:33.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:214 -   • 目录可读: ✓
2025-07-28 13:20:33.854 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:215 -   • 目录可写: ✓
2025-07-28 13:20:33.855 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:223 -   • 可用空间: 155472.12 MB / 471482.08 MB
2025-07-28 13:20:33.855 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:239 - [INFO] Java字体系统属性:
2025-07-28 13:20:33.855 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • java.awt.fonts: 未设置
2025-07-28 13:20:33.855 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • sun.java2d.fontpath: 未设置
2025-07-28 13:20:33.855 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • java.awt.headless: true
2025-07-28 13:20:33.855 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • file.encoding: UTF-8
2025-07-28 13:20:33.855 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • sun.jnu.encoding: UTF-8
2025-07-28 13:20:33.855 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:261 - [INFO] 字体相关环境变量:
2025-07-28 13:20:33.856 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FONTCONFIG_PATH: 未设置
2025-07-28 13:20:33.856 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FONTCONFIG_FILE: 未设置
2025-07-28 13:20:33.856 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FC_DEBUG: 未设置
2025-07-28 13:20:33.857 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:280 - 检查系统字体目录:
2025-07-28 13:20:33.857 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   /usr/share/fonts - 不存在
2025-07-28 13:20:33.857 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   /usr/local/share/fonts - 不存在
2025-07-28 13:20:33.858 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:288 -   /System/Library/Fonts - 存在 (81 个字体文件)
2025-07-28 13:20:33.859 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:288 -   /Library/Fonts - 存在 (1 个字体文件)
2025-07-28 13:20:33.859 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   C:\Windows\Fonts - 不存在
2025-07-28 13:20:33.859 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:201 - ✓ [环境诊断] 环境诊断检查完成
2025-07-28 13:20:33.859 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:309 - --- 字体文件设置开始 ---
2025-07-28 13:20:33.860 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:319 - 字体资源URL: file:/Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts, 协议: file
2025-07-28 13:20:33.860 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:326 - 本地文件系统字体路径: /Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts
2025-07-28 13:20:33.860 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:622 - --- 字体文件分析开始 ---
2025-07-28 13:20:33.862 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:660 - 字体文件统计:
2025-07-28 13:20:33.862 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ini 格式: 1 个文件
2025-07-28 13:20:33.862 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .fon 格式: 192 个文件
2025-07-28 13:20:33.863 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ttc 格式: 14 个文件
2025-07-28 13:20:33.863 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .zip 格式: 1 个文件
2025-07-28 13:20:33.863 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ttf 格式: 146 个文件
2025-07-28 13:20:33.863 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .dat 格式: 1 个文件
2025-07-28 13:20:33.863 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .xml 格式: 1 个文件
2025-07-28 13:20:33.863 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:664 - 发现的优先中文字体: [Dengb.ttf, SimsunExtG.ttf, NotoSerifSC-VF.ttf, Deng.ttf, NotoSansSC-VF.ttf, HYZhongHeiTi-197.ttf, Dengl.ttf]
2025-07-28 13:20:33.863 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:665 - 发现的优先英文字体: [times.ttf, consola.ttf, arial.ttf, calibri.ttf]
2025-07-28 13:20:33.863 WARN  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:670 - 发现 192 个 .fon 格式字体文件，在Linux环境下可能不兼容
2025-07-28 13:20:33.863 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:673 - --- 字体文件分析完成 ---
2025-07-28 13:20:33.863 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:344 - --- 字体文件设置完成 ---
2025-07-28 13:20:33.863 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:680 - --- 高级字体设置开始 ---
2025-07-28 13:20:33.977 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:694 - 添加字体文件夹源: /Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts
2025-07-28 13:20:33.977 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:700 - 添加系统字体源
2025-07-28 13:20:33.984 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:718 - 配置字体替换规则...
2025-07-28 13:20:33.984 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:741 - 字体替换规则配置完成
2025-07-28 13:20:33.984 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:748 - 配置字体回退设置...
2025-07-28 13:20:33.986 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:755 - 已加载Noto字体回退设置
2025-07-28 13:20:33.986 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:763 - 字体回退设置配置完成
2025-07-28 13:20:33.986 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:711 - --- 高级字体设置完成 ---
2025-07-28 13:20:33.986 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:779 - --- 字体加载状态验证开始 ---
2025-07-28 13:20:33.986 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:785 - 配置的字体源数量: 2
2025-07-28 13:20:33.986 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:789 - 字体源 1: com.aspose.words.FolderFontSource@7de772d9 (类型: FolderFontSource)
2025-07-28 13:20:33.986 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:793 -   字体源类型: FolderFontSource
2025-07-28 13:20:33.986 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:794 -   字体源配置完成
2025-07-28 13:20:33.986 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:789 - 字体源 2: com.aspose.words.SystemFontSource@12ad988d (类型: SystemFontSource)
2025-07-28 13:20:33.986 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:793 -   字体源类型: SystemFontSource
2025-07-28 13:20:33.986 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:794 -   字体源配置完成
2025-07-28 13:20:33.986 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:812 - 测试字体解析能力:
2025-07-28 13:20:33.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'SimSun' - 解析测试通过
2025-07-28 13:20:33.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 '宋体' - 解析测试通过
2025-07-28 13:20:33.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'Microsoft YaHei' - 解析测试通过
2025-07-28 13:20:33.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 '微软雅黑' - 解析测试通过
2025-07-28 13:20:33.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'Arial' - 解析测试通过
2025-07-28 13:20:33.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:804 - --- 字体加载状态验证完成 ---
2025-07-28 13:20:33.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1350 - ▶ [可用性验证] 开始字体可用性验证...
2025-07-28 13:20:33.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1368 - [INFO] 中文字体可用性检查:
2025-07-28 13:20:33.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • NotoSansSC-VF - ✓可用 (直接字体文件)
2025-07-28 13:20:33.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • SimSun - ✓可用 (替换为: NotoSansSC-VF.ttf)
2025-07-28 13:20:33.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • Microsoft YaHei - ✓可用 (替换为: HYZhongHeiTi-197.ttf)
2025-07-28 13:20:33.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • HYZhongHeiTi-197 - ✓可用 (直接字体文件)
2025-07-28 13:20:33.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1404 - [INFO] 字体替换规则验证:
2025-07-28 13:20:33.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1411 -   • 表格替换规则: ✓已启用
2025-07-28 13:20:33.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1414 -   • 字体信息替换: ✓已启用
2025-07-28 13:20:33.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1417 -   • 默认字体替换: ✓已启用
2025-07-28 13:20:33.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1420 -   • 默认字体: NotoSansSC-VF.ttf
2025-07-28 13:20:33.988 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1432 - [INFO] 字体文件完整性验证:
2025-07-28 13:20:33.988 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1435 -   • 使用本地字体目录，跳过临时文件验证
2025-07-28 13:20:33.988 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1361 - ✓ [可用性验证] 字体可用性验证完成
2025-07-28 13:20:33.988 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:164 - ============================================================
2025-07-28 13:20:33.988 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:165 - ========== 字体系统诊断完成 ==========
2025-07-28 13:20:33.988 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:166 - 完成时间: 2025-07-28 13:20:33
2025-07-28 13:20:33.988 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:167 - ============================================================
2025-07-28 13:20:33.988 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1002 - 开始docx转html转换: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 13:20:33.988 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:868 - ============================================================
2025-07-28 13:20:33.988 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:869 - ========== 强制字体系统诊断开始 ==========
2025-07-28 13:20:33.988 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:870 - 诊断时间: 2025-07-28 13:20:33
2025-07-28 13:20:33.988 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:871 - 字体初始化状态: 已初始化
2025-07-28 13:20:33.988 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:872 - ============================================================
2025-07-28 13:20:33.988 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:896 - ▶ [环境检查] 运行环境: 本地环境
2025-07-28 13:20:33.988 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:897 - ▶ [环境检查] 操作系统: Mac OS X
2025-07-28 13:20:33.988 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:898 - ▶ [环境检查] 工作目录: /Users/<USER>/IdeaProjects/mcp-server
2025-07-28 13:20:33.988 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:899 - ▶ [环境检查] 临时目录: /var/folders/qs/761__31103sc01c7b0m4vxth0000gp/T/
2025-07-28 13:20:33.988 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:906 - ▶ [字体文件] 开始字体文件状态检查...
2025-07-28 13:20:33.989 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:931 - [INFO] 使用本地字体目录（非JAR环境）
2025-07-28 13:20:33.989 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:939 - ▶ [字体设置] 开始字体设置验证...
2025-07-28 13:20:33.989 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:945 - [INFO] 配置的字体源数量: 2
2025-07-28 13:20:33.989 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 1: com.aspose.words.FolderFontSource@7de772d9 (类型: FolderFontSource)
2025-07-28 13:20:33.989 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 2: com.aspose.words.SystemFontSource@12ad988d (类型: SystemFontSource)
2025-07-28 13:20:33.989 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:956 - [INFO] 字体替换规则状态:
2025-07-28 13:20:33.989 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:957 -   • 表格替换规则: ✓已启用
2025-07-28 13:20:33.989 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:958 -   • 默认字体替换: ✓已启用
2025-07-28 13:20:33.989 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:960 -   • 默认字体: NotoSansSC-VF.ttf
2025-07-28 13:20:33.989 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1368 - [INFO] 中文字体可用性检查:
2025-07-28 13:20:33.989 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • NotoSansSC-VF - ✓可用 (直接字体文件)
2025-07-28 13:20:33.989 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • SimSun - ✓可用 (替换为: NotoSansSC-VF.ttf)
2025-07-28 13:20:33.989 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • Microsoft YaHei - ✓可用 (替换为: HYZhongHeiTi-197.ttf)
2025-07-28 13:20:33.989 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • HYZhongHeiTi-197 - ✓可用 (直接字体文件)
2025-07-28 13:20:33.989 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:886 - ============================================================
2025-07-28 13:20:33.989 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:887 - ========== 强制字体系统诊断完成 ==========
2025-07-28 13:20:33.989 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:888 - 完成时间: 2025-07-28 13:20:33
2025-07-28 13:20:33.989 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:889 - ============================================================
2025-07-28 13:20:33.989 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1008 - ▶ [文档加载] 开始加载文档: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 13:20:34.920 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1010 - ✓ [文档加载] 文档加载成功，开始应用字体设置
2025-07-28 13:20:34.920 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1014 - ✓ [字体设置] 全局字体设置已应用到文档
2025-07-28 13:20:34.920 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1063 - ▶ [字体分析] 开始分析文档字体使用情况: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 13:20:34.921 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1070 - [INFO] 文档字体源配置: 2 个字体源
2025-07-28 13:20:34.921 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1074 -   字体源 1: FolderFontSource
2025-07-28 13:20:34.921 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1074 -   字体源 2: SystemFontSource
2025-07-28 13:20:34.986 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1104 - [INFO] 文档中使用的字体: 宋体, 仿宋, Times New Roman, Wingdings 2
2025-07-28 13:20:34.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1112 - [INFO] 包含中文字体: 是
2025-07-28 13:20:34.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1081 - ✓ [字体分析] 文档字体使用情况分析完成
2025-07-28 13:20:34.987 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1025 - ▶ [转换执行] 开始执行docx转html转换...
2025-07-28 13:20:36.915 ERROR c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1047 - ✗ [转换失败] docx转html转换失败: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx - 错误详情: Image file cannot be written to disk. When saving to a stream or to a string either ImagesFolder should be specified, or custom streams should be provided via ImageSavingCallback, or ExportImagesAsBase64 should be set to true. Please see documentation for details.
java.lang.IllegalStateException: Image file cannot be written to disk. When saving to a stream or to a string either ImagesFolder should be specified, or custom streams should be provided via ImageSavingCallback, or ExportImagesAsBase64 should be set to true. Please see documentation for details.
	at com.aspose.words.zzVWC.zzW4y(Unknown Source)
	at com.aspose.words.zzI6.zzZ2u(Unknown Source)
	at com.aspose.words.zzne.zzBF(Unknown Source)
	at com.aspose.words.zzne.zzZ2u(Unknown Source)
	at com.aspose.words.zzne.zzZ2u(Unknown Source)
	at com.aspose.words.zzne.zzZ2u(Unknown Source)
	at com.aspose.words.zzne.zzZ2u(Unknown Source)
	at com.aspose.words.zzne.zzZ2u(Unknown Source)
	at com.aspose.words.zz2v.zzZTd(Unknown Source)
	at com.aspose.words.zzVTe.zzRR(Unknown Source)
	at com.aspose.words.zzX0Z.visitShapeStart(Unknown Source)
	at com.aspose.words.Shape.acceptStart(Unknown Source)
	at com.aspose.words.CompositeNode.acceptCore(Unknown Source)
	at com.aspose.words.Shape.accept(Unknown Source)
	at com.aspose.words.CompositeNode.acceptChildren(Unknown Source)
	at com.aspose.words.CompositeNode.acceptCore(Unknown Source)
	at com.aspose.words.Paragraph.accept(Unknown Source)
	at com.aspose.words.CompositeNode.acceptChildren(Unknown Source)
	at com.aspose.words.CompositeNode.acceptCore(Unknown Source)
	at com.aspose.words.Body.accept(Unknown Source)
	at com.aspose.words.zzZlh.zznG(Unknown Source)
	at com.aspose.words.zzZlh.zzZ2u(Unknown Source)
	at com.aspose.words.zzZlh.zz0l(Unknown Source)
	at com.aspose.words.zzZlh.zzYGq(Unknown Source)
	at com.aspose.words.zzZlh.zzY4h(Unknown Source)
	at com.aspose.words.zzZlh.zzrF(Unknown Source)
	at com.aspose.words.zzXfB.zzZ2u(Unknown Source)
	at com.aspose.words.zzXCv.zzZ2u(Unknown Source)
	at com.aspose.words.Document.zz0l(Unknown Source)
	at com.aspose.words.Document.zzZ2u(Unknown Source)
	at com.aspose.words.Document.zzZ2u(Unknown Source)
	at com.aspose.words.Document.zzXJY(Unknown Source)
	at com.aspose.words.Document.save(Unknown Source)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.lambda$executeDocxToHtmlConversion$16(AsposeConvertServiceImpl.java:983)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.executeConversion(AsposeConvertServiceImpl.java:1026)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.executeDocxToHtmlConversion(AsposeConvertServiceImpl.java:983)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertRuleImpl.executeConvertRule(AsposeConvertRuleImpl.java:85)
	at com.jd.jdt.joylaw.ai.mcp.controller.AsposeConvertControlle.officeConvert(AsposeConvertControlle.java:41)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onComplete(FluxPeekFuseable.java:277)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:889)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:798)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-28 13:20:36.917 ERROR c.j.j.j.a.m.s.i.AsposeConvertRuleImpl:91 - 规则引擎执行失败: docx转html转换失败: Image file cannot be written to disk. When saving to a stream or to a string either ImagesFolder should be specified, or custom streams should be provided via ImageSavingCallback, or ExportImagesAsBase64 should be set to true. Please see documentation for details.
java.lang.RuntimeException: docx转html转换失败: Image file cannot be written to disk. When saving to a stream or to a string either ImagesFolder should be specified, or custom streams should be provided via ImageSavingCallback, or ExportImagesAsBase64 should be set to true. Please see documentation for details.
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.executeConversion(AsposeConvertServiceImpl.java:1055)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.executeDocxToHtmlConversion(AsposeConvertServiceImpl.java:983)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertRuleImpl.executeConvertRule(AsposeConvertRuleImpl.java:85)
	at com.jd.jdt.joylaw.ai.mcp.controller.AsposeConvertControlle.officeConvert(AsposeConvertControlle.java:41)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onComplete(FluxPeekFuseable.java:277)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:889)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:798)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.IllegalStateException: Image file cannot be written to disk. When saving to a stream or to a string either ImagesFolder should be specified, or custom streams should be provided via ImageSavingCallback, or ExportImagesAsBase64 should be set to true. Please see documentation for details.
	at com.aspose.words.zzVWC.zzW4y(Unknown Source)
	at com.aspose.words.zzI6.zzZ2u(Unknown Source)
	at com.aspose.words.zzne.zzBF(Unknown Source)
	at com.aspose.words.zzne.zzZ2u(Unknown Source)
	at com.aspose.words.zzne.zzZ2u(Unknown Source)
	at com.aspose.words.zzne.zzZ2u(Unknown Source)
	at com.aspose.words.zzne.zzZ2u(Unknown Source)
	at com.aspose.words.zzne.zzZ2u(Unknown Source)
	at com.aspose.words.zz2v.zzZTd(Unknown Source)
	at com.aspose.words.zzVTe.zzRR(Unknown Source)
	at com.aspose.words.zzX0Z.visitShapeStart(Unknown Source)
	at com.aspose.words.Shape.acceptStart(Unknown Source)
	at com.aspose.words.CompositeNode.acceptCore(Unknown Source)
	at com.aspose.words.Shape.accept(Unknown Source)
	at com.aspose.words.CompositeNode.acceptChildren(Unknown Source)
	at com.aspose.words.CompositeNode.acceptCore(Unknown Source)
	at com.aspose.words.Paragraph.accept(Unknown Source)
	at com.aspose.words.CompositeNode.acceptChildren(Unknown Source)
	at com.aspose.words.CompositeNode.acceptCore(Unknown Source)
	at com.aspose.words.Body.accept(Unknown Source)
	at com.aspose.words.zzZlh.zznG(Unknown Source)
	at com.aspose.words.zzZlh.zzZ2u(Unknown Source)
	at com.aspose.words.zzZlh.zz0l(Unknown Source)
	at com.aspose.words.zzZlh.zzYGq(Unknown Source)
	at com.aspose.words.zzZlh.zzY4h(Unknown Source)
	at com.aspose.words.zzZlh.zzrF(Unknown Source)
	at com.aspose.words.zzXfB.zzZ2u(Unknown Source)
	at com.aspose.words.zzXCv.zzZ2u(Unknown Source)
	at com.aspose.words.Document.zz0l(Unknown Source)
	at com.aspose.words.Document.zzZ2u(Unknown Source)
	at com.aspose.words.Document.zzZ2u(Unknown Source)
	at com.aspose.words.Document.zzXJY(Unknown Source)
	at com.aspose.words.Document.save(Unknown Source)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.lambda$executeDocxToHtmlConversion$16(AsposeConvertServiceImpl.java:983)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.executeConversion(AsposeConvertServiceImpl.java:1026)
	... 70 common frames omitted
2025-07-28 13:20:36.924 ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler:102 - [9fdf515a-4]  500 Server Error for HTTP POST "/api/v1/aspose-convert/word"
java.io.IOException: 规则引擎执行失败: docx转html转换失败: Image file cannot be written to disk. When saving to a stream or to a string either ImagesFolder should be specified, or custom streams should be provided via ImageSavingCallback, or ExportImagesAsBase64 should be set to true. Please see documentation for details.
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertRuleImpl.executeConvertRule(AsposeConvertRuleImpl.java:93)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.MonoFlatMap] :
	reactor.core.publisher.Mono.flatMap(Mono.java:3179)
	org.springframework.web.reactive.result.method.InvocableHandlerMethod.invoke(InvocableHandlerMethod.java:189)
Error has been observed at the following site(s):
	*________Mono.flatMap ⇢ at org.springframework.web.reactive.result.method.InvocableHandlerMethod.invoke(InvocableHandlerMethod.java:189)
	*__________Mono.defer ⇢ at org.springframework.web.reactive.result.method.annotation.RequestMappingHandlerAdapter.handle(RequestMappingHandlerAdapter.java:283)
	*___________Mono.then ⇢ at org.springframework.web.reactive.result.method.annotation.RequestMappingHandlerAdapter.handle(RequestMappingHandlerAdapter.java:283)
	|_      Mono.doOnNext ⇢ at org.springframework.web.reactive.result.method.annotation.RequestMappingHandlerAdapter.handle(RequestMappingHandlerAdapter.java:284)
	|_ Mono.onErrorResume ⇢ at org.springframework.web.reactive.result.method.annotation.RequestMappingHandlerAdapter.handle(RequestMappingHandlerAdapter.java:285)
	|_ Mono.onErrorResume ⇢ at org.springframework.web.reactive.DispatcherHandler.handleResultMono(DispatcherHandler.java:168)
	|_       Mono.flatMap ⇢ at org.springframework.web.reactive.DispatcherHandler.handleResultMono(DispatcherHandler.java:172)
	*__________Mono.error ⇢ at org.springframework.web.reactive.result.method.annotation.RequestMappingHandlerAdapter.handleException(RequestMappingHandlerAdapter.java:340)
	*__________Mono.error ⇢ at org.springframework.web.reactive.result.method.annotation.RequestMappingHandlerAdapter.handleException(RequestMappingHandlerAdapter.java:340)
	*________Mono.flatMap ⇢ at org.springframework.web.reactive.DispatcherHandler.handle(DispatcherHandler.java:154)
	*__________Mono.defer ⇢ at org.springframework.web.server.handler.DefaultWebFilterChain.filter(DefaultWebFilterChain.java:106)
	|_     Mono.doOnError ⇢ at org.springframework.web.server.handler.ExceptionHandlingWebHandler.handle(ExceptionHandlingWebHandler.java:84)
	|_ Mono.onErrorResume ⇢ at org.springframework.web.server.handler.ExceptionHandlingWebHandler.handle(ExceptionHandlingWebHandler.java:85)
	|_     Mono.doOnError ⇢ at org.springframework.web.server.handler.ExceptionHandlingWebHandler.handle(ExceptionHandlingWebHandler.java:84)
	*__________Mono.error ⇢ at org.springframework.web.server.handler.ExceptionHandlingWebHandler$CheckpointInsertingHandler.handle(ExceptionHandlingWebHandler.java:106)
	|_         checkpoint ⇢ HTTP POST "/api/v1/aspose-convert/word" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertRuleImpl.executeConvertRule(AsposeConvertRuleImpl.java:93)
		at com.jd.jdt.joylaw.ai.mcp.controller.AsposeConvertControlle.officeConvert(AsposeConvertControlle.java:41)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
		at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.base/java.lang.reflect.Method.invoke(Method.java:569)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
		at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onComplete(FluxPeekFuseable.java:277)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:889)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:798)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: docx转html转换失败: Image file cannot be written to disk. When saving to a stream or to a string either ImagesFolder should be specified, or custom streams should be provided via ImageSavingCallback, or ExportImagesAsBase64 should be set to true. Please see documentation for details.
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.executeConversion(AsposeConvertServiceImpl.java:1055)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.executeDocxToHtmlConversion(AsposeConvertServiceImpl.java:983)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertRuleImpl.executeConvertRule(AsposeConvertRuleImpl.java:85)
	at com.jd.jdt.joylaw.ai.mcp.controller.AsposeConvertControlle.officeConvert(AsposeConvertControlle.java:41)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onComplete(FluxPeekFuseable.java:277)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:889)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:798)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.IllegalStateException: Image file cannot be written to disk. When saving to a stream or to a string either ImagesFolder should be specified, or custom streams should be provided via ImageSavingCallback, or ExportImagesAsBase64 should be set to true. Please see documentation for details.
	at com.aspose.words.zzVWC.zzW4y(Unknown Source)
	at com.aspose.words.zzI6.zzZ2u(Unknown Source)
	at com.aspose.words.zzne.zzBF(Unknown Source)
	at com.aspose.words.zzne.zzZ2u(Unknown Source)
	at com.aspose.words.zzne.zzZ2u(Unknown Source)
	at com.aspose.words.zzne.zzZ2u(Unknown Source)
	at com.aspose.words.zzne.zzZ2u(Unknown Source)
	at com.aspose.words.zzne.zzZ2u(Unknown Source)
	at com.aspose.words.zz2v.zzZTd(Unknown Source)
	at com.aspose.words.zzVTe.zzRR(Unknown Source)
	at com.aspose.words.zzX0Z.visitShapeStart(Unknown Source)
	at com.aspose.words.Shape.acceptStart(Unknown Source)
	at com.aspose.words.CompositeNode.acceptCore(Unknown Source)
	at com.aspose.words.Shape.accept(Unknown Source)
	at com.aspose.words.CompositeNode.acceptChildren(Unknown Source)
	at com.aspose.words.CompositeNode.acceptCore(Unknown Source)
	at com.aspose.words.Paragraph.accept(Unknown Source)
	at com.aspose.words.CompositeNode.acceptChildren(Unknown Source)
	at com.aspose.words.CompositeNode.acceptCore(Unknown Source)
	at com.aspose.words.Body.accept(Unknown Source)
	at com.aspose.words.zzZlh.zznG(Unknown Source)
	at com.aspose.words.zzZlh.zzZ2u(Unknown Source)
	at com.aspose.words.zzZlh.zz0l(Unknown Source)
	at com.aspose.words.zzZlh.zzYGq(Unknown Source)
	at com.aspose.words.zzZlh.zzY4h(Unknown Source)
	at com.aspose.words.zzZlh.zzrF(Unknown Source)
	at com.aspose.words.zzXfB.zzZ2u(Unknown Source)
	at com.aspose.words.zzXCv.zzZ2u(Unknown Source)
	at com.aspose.words.Document.zz0l(Unknown Source)
	at com.aspose.words.Document.zzZ2u(Unknown Source)
	at com.aspose.words.Document.zzZ2u(Unknown Source)
	at com.aspose.words.Document.zzXJY(Unknown Source)
	at com.aspose.words.Document.save(Unknown Source)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.lambda$executeDocxToHtmlConversion$16(AsposeConvertServiceImpl.java:983)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.executeConversion(AsposeConvertServiceImpl.java:1026)
	... 70 common frames omitted
2025-07-28 13:23:33.255 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1002 - 开始docx转html转换: 【样例】技术服务协议模板（jd为销售方通用版）.docx
2025-07-28 13:23:33.256 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:868 - ============================================================
2025-07-28 13:23:33.256 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:869 - ========== 强制字体系统诊断开始 ==========
2025-07-28 13:23:33.256 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:870 - 诊断时间: 2025-07-28 13:23:33
2025-07-28 13:23:33.256 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:871 - 字体初始化状态: 已初始化
2025-07-28 13:23:33.256 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:872 - ============================================================
2025-07-28 13:23:33.257 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:896 - ▶ [环境检查] 运行环境: 本地环境
2025-07-28 13:23:33.257 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:897 - ▶ [环境检查] 操作系统: Mac OS X
2025-07-28 13:23:33.257 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:898 - ▶ [环境检查] 工作目录: /Users/<USER>/IdeaProjects/mcp-server
2025-07-28 13:23:33.257 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:899 - ▶ [环境检查] 临时目录: /var/folders/qs/761__31103sc01c7b0m4vxth0000gp/T/
2025-07-28 13:23:33.258 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:906 - ▶ [字体文件] 开始字体文件状态检查...
2025-07-28 13:23:33.258 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:931 - [INFO] 使用本地字体目录（非JAR环境）
2025-07-28 13:23:33.258 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:939 - ▶ [字体设置] 开始字体设置验证...
2025-07-28 13:23:33.258 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:945 - [INFO] 配置的字体源数量: 2
2025-07-28 13:23:33.258 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 1: com.aspose.words.FolderFontSource@7de772d9 (类型: FolderFontSource)
2025-07-28 13:23:33.258 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 2: com.aspose.words.SystemFontSource@12ad988d (类型: SystemFontSource)
2025-07-28 13:23:33.259 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:956 - [INFO] 字体替换规则状态:
2025-07-28 13:23:33.259 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:957 -   • 表格替换规则: ✓已启用
2025-07-28 13:23:33.260 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:958 -   • 默认字体替换: ✓已启用
2025-07-28 13:23:33.260 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:960 -   • 默认字体: NotoSansSC-VF.ttf
2025-07-28 13:23:33.260 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1368 - [INFO] 中文字体可用性检查:
2025-07-28 13:23:33.260 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • NotoSansSC-VF - ✓可用 (直接字体文件)
2025-07-28 13:23:33.260 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • SimSun - ✓可用 (替换为: NotoSansSC-VF.ttf)
2025-07-28 13:23:33.260 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1384 -   • Microsoft YaHei - ✓可用 (替换为: HYZhongHeiTi-197.ttf)
2025-07-28 13:23:33.260 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1389 -   • HYZhongHeiTi-197 - ✓可用 (直接字体文件)
2025-07-28 13:23:33.260 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:886 - ============================================================
2025-07-28 13:23:33.260 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:887 - ========== 强制字体系统诊断完成 ==========
2025-07-28 13:23:33.260 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:888 - 完成时间: 2025-07-28 13:23:33
2025-07-28 13:23:33.260 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:889 - ============================================================
2025-07-28 13:23:33.260 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1008 - ▶ [文档加载] 开始加载文档: 【样例】技术服务协议模板（jd为销售方通用版）.docx
2025-07-28 13:23:33.294 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1010 - ✓ [文档加载] 文档加载成功，开始应用字体设置
2025-07-28 13:23:33.294 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1014 - ✓ [字体设置] 全局字体设置已应用到文档
2025-07-28 13:23:33.294 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1063 - ▶ [字体分析] 开始分析文档字体使用情况: 【样例】技术服务协议模板（jd为销售方通用版）.docx
2025-07-28 13:23:33.294 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1070 - [INFO] 文档字体源配置: 2 个字体源
2025-07-28 13:23:33.294 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1074 -   字体源 1: FolderFontSource
2025-07-28 13:23:33.294 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1074 -   字体源 2: SystemFontSource
2025-07-28 13:23:33.296 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1104 - [INFO] 文档中使用的字体: 宋体, Calibri, 仿宋, 微软雅黑
2025-07-28 13:23:33.296 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1112 - [INFO] 包含中文字体: 是
2025-07-28 13:23:33.296 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1081 - ✓ [字体分析] 文档字体使用情况分析完成
2025-07-28 13:23:33.296 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1025 - ▶ [转换执行] 开始执行docx转html转换...
2025-07-28 13:23:33.728 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1027 - ✓ [转换执行] docx转html转换执行完成
2025-07-28 13:23:33.728 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1035 - ✓ [转换完成] docx转html转换成功完成:
2025-07-28 13:23:33.728 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1036 -   • 源文件: 【样例】技术服务协议模板（jd为销售方通用版）.docx (输入大小: 51205 bytes)
2025-07-28 13:23:33.728 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1037 -   • 目标文件: 【样例】技术服务协议模板（jd为销售方通用版）.html (输出大小: 128302 bytes)
2025-07-28 13:23:33.729 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1038 -   • 压缩比: 250.57%
2025-07-28 13:23:33.729 INFO  c.j.j.j.a.m.s.i.AsposeConvertRuleImpl:86 - 规则引擎执行成功：.docx -> .html
2025-07-28 13:28:03.513 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 13:28:09.900 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-28 13:28:09.930 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 23382 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 13:28:09.930 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 13:28:10.274 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=dc45cc65-8b60-3b9e-a5fd-0ca3aa608d29
2025-07-28 13:28:10.709 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 13:28:10.720 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.123 seconds (process running for 2.154)
2025-07-28 13:28:42.566 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:153 - ============================================================
2025-07-28 13:28:42.567 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:154 - ========== 字体系统诊断开始 ==========
2025-07-28 13:28:42.570 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:155 - 诊断时间: 2025-07-28 13:28:42
2025-07-28 13:28:42.570 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:156 - ============================================================
2025-07-28 13:28:42.570 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:174 - ▶ [环境诊断] 开始环境诊断检查...
2025-07-28 13:28:42.570 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:183 - [INFO] 运行环境信息:
2025-07-28 13:28:42.570 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:184 -   • 操作系统: Mac OS X 15.5
2025-07-28 13:28:42.570 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:185 -   • Java版本: 17.0.15 (Microsoft)
2025-07-28 13:28:42.570 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:186 -   • 工作目录: /Users/<USER>/IdeaProjects/mcp-server
2025-07-28 13:28:42.571 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:190 -   • 运行环境: 本地环境
2025-07-28 13:28:42.571 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:211 - [INFO] 临时目录状态:
2025-07-28 13:28:42.571 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:212 -   • 临时目录路径: /var/folders/qs/761__31103sc01c7b0m4vxth0000gp/T/
2025-07-28 13:28:42.571 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:213 -   • 目录存在: ✓
2025-07-28 13:28:42.571 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:214 -   • 目录可读: ✓
2025-07-28 13:28:42.571 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:215 -   • 目录可写: ✓
2025-07-28 13:28:42.573 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:223 -   • 可用空间: 155717.61 MB / 471482.08 MB
2025-07-28 13:28:42.573 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:239 - [INFO] Java字体系统属性:
2025-07-28 13:28:42.573 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • java.awt.fonts: 未设置
2025-07-28 13:28:42.574 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • sun.java2d.fontpath: 未设置
2025-07-28 13:28:42.574 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • java.awt.headless: true
2025-07-28 13:28:42.574 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • file.encoding: UTF-8
2025-07-28 13:28:42.574 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • sun.jnu.encoding: UTF-8
2025-07-28 13:28:42.574 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:261 - [INFO] 字体相关环境变量:
2025-07-28 13:28:42.574 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FONTCONFIG_PATH: 未设置
2025-07-28 13:28:42.574 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FONTCONFIG_FILE: 未设置
2025-07-28 13:28:42.574 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FC_DEBUG: 未设置
2025-07-28 13:28:42.574 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:280 - 检查系统字体目录:
2025-07-28 13:28:42.575 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   /usr/share/fonts - 不存在
2025-07-28 13:28:42.575 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   /usr/local/share/fonts - 不存在
2025-07-28 13:28:42.576 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:288 -   /System/Library/Fonts - 存在 (81 个字体文件)
2025-07-28 13:28:42.577 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:288 -   /Library/Fonts - 存在 (1 个字体文件)
2025-07-28 13:28:42.578 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   C:\Windows\Fonts - 不存在
2025-07-28 13:28:42.578 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:201 - ✓ [环境诊断] 环境诊断检查完成
2025-07-28 13:28:42.578 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:309 - --- 字体文件设置开始 ---
2025-07-28 13:28:42.578 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:319 - 字体资源URL: file:/Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts, 协议: file
2025-07-28 13:28:42.578 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:326 - 本地文件系统字体路径: /Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts
2025-07-28 13:28:42.578 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:622 - --- 字体文件分析开始 ---
2025-07-28 13:28:42.581 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:660 - 字体文件统计:
2025-07-28 13:28:42.581 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ini 格式: 1 个文件
2025-07-28 13:28:42.581 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .fon 格式: 192 个文件
2025-07-28 13:28:42.581 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ttc 格式: 14 个文件
2025-07-28 13:28:42.581 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .zip 格式: 1 个文件
2025-07-28 13:28:42.581 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ttf 格式: 146 个文件
2025-07-28 13:28:42.581 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .dat 格式: 1 个文件
2025-07-28 13:28:42.581 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .xml 格式: 1 个文件
2025-07-28 13:28:42.581 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:664 - 发现的优先中文字体: [Dengb.ttf, SimsunExtG.ttf, NotoSerifSC-VF.ttf, Deng.ttf, NotoSansSC-VF.ttf, HYZhongHeiTi-197.ttf, Dengl.ttf]
2025-07-28 13:28:42.582 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:665 - 发现的优先英文字体: [times.ttf, consola.ttf, arial.ttf, calibri.ttf]
2025-07-28 13:28:42.582 WARN  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:670 - 发现 192 个 .fon 格式字体文件，在Linux环境下可能不兼容
2025-07-28 13:28:42.582 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:673 - --- 字体文件分析完成 ---
2025-07-28 13:28:42.582 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:344 - --- 字体文件设置完成 ---
2025-07-28 13:28:42.582 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:680 - --- 高级字体设置开始 ---
2025-07-28 13:28:42.678 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:694 - 添加字体文件夹源: /Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts
2025-07-28 13:28:42.678 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:700 - 添加系统字体源
2025-07-28 13:28:42.687 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:718 - 配置字体替换规则...
2025-07-28 13:28:42.687 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:741 - 字体替换规则配置完成
2025-07-28 13:28:42.687 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:748 - 配置字体回退设置...
2025-07-28 13:28:42.689 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:755 - 已加载Noto字体回退设置
2025-07-28 13:28:42.689 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:763 - 字体回退设置配置完成
2025-07-28 13:28:42.689 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:711 - --- 高级字体设置完成 ---
2025-07-28 13:28:42.689 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:779 - --- 字体加载状态验证开始 ---
2025-07-28 13:28:42.689 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:785 - 配置的字体源数量: 2
2025-07-28 13:28:42.690 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:789 - 字体源 1: com.aspose.words.FolderFontSource@70779957 (类型: FolderFontSource)
2025-07-28 13:28:42.690 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:793 -   字体源类型: FolderFontSource
2025-07-28 13:28:42.690 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:794 -   字体源配置完成
2025-07-28 13:28:42.690 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:789 - 字体源 2: com.aspose.words.SystemFontSource@336d32a6 (类型: SystemFontSource)
2025-07-28 13:28:42.690 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:793 -   字体源类型: SystemFontSource
2025-07-28 13:28:42.690 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:794 -   字体源配置完成
2025-07-28 13:28:42.690 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:812 - 测试字体解析能力:
2025-07-28 13:28:42.690 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'SimSun' - 解析测试通过
2025-07-28 13:28:42.690 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 '宋体' - 解析测试通过
2025-07-28 13:28:42.690 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'Microsoft YaHei' - 解析测试通过
2025-07-28 13:28:42.690 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 '微软雅黑' - 解析测试通过
2025-07-28 13:28:42.690 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'Arial' - 解析测试通过
2025-07-28 13:28:42.690 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:804 - --- 字体加载状态验证完成 ---
2025-07-28 13:28:42.690 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1372 - ▶ [可用性验证] 开始字体可用性验证...
2025-07-28 13:28:42.690 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1390 - [INFO] 中文字体可用性检查:
2025-07-28 13:28:42.690 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1411 -   • NotoSansSC-VF - ✓可用 (直接字体文件)
2025-07-28 13:28:42.691 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1406 -   • SimSun - ✓可用 (替换为: NotoSansSC-VF.ttf)
2025-07-28 13:28:42.691 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1406 -   • Microsoft YaHei - ✓可用 (替换为: HYZhongHeiTi-197.ttf)
2025-07-28 13:28:42.691 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1411 -   • HYZhongHeiTi-197 - ✓可用 (直接字体文件)
2025-07-28 13:28:42.692 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1426 - [INFO] 字体替换规则验证:
2025-07-28 13:28:42.692 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1433 -   • 表格替换规则: ✓已启用
2025-07-28 13:28:42.692 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1436 -   • 字体信息替换: ✓已启用
2025-07-28 13:28:42.692 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1439 -   • 默认字体替换: ✓已启用
2025-07-28 13:28:42.692 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1442 -   • 默认字体: NotoSansSC-VF.ttf
2025-07-28 13:28:42.692 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1454 - [INFO] 字体文件完整性验证:
2025-07-28 13:28:42.692 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1457 -   • 使用本地字体目录，跳过临时文件验证
2025-07-28 13:28:42.692 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1383 - ✓ [可用性验证] 字体可用性验证完成
2025-07-28 13:28:42.692 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:164 - ============================================================
2025-07-28 13:28:42.692 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:165 - ========== 字体系统诊断完成 ==========
2025-07-28 13:28:42.692 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:166 - 完成时间: 2025-07-28 13:28:42
2025-07-28 13:28:42.692 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:167 - ============================================================
2025-07-28 13:28:42.692 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1024 - 开始docx转html转换: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 13:28:42.692 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:868 - ============================================================
2025-07-28 13:28:42.692 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:869 - ========== 强制字体系统诊断开始 ==========
2025-07-28 13:28:42.693 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:870 - 诊断时间: 2025-07-28 13:28:42
2025-07-28 13:28:42.693 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:871 - 字体初始化状态: 已初始化
2025-07-28 13:28:42.693 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:872 - ============================================================
2025-07-28 13:28:42.693 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:896 - ▶ [环境检查] 运行环境: 本地环境
2025-07-28 13:28:42.693 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:897 - ▶ [环境检查] 操作系统: Mac OS X
2025-07-28 13:28:42.693 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:898 - ▶ [环境检查] 工作目录: /Users/<USER>/IdeaProjects/mcp-server
2025-07-28 13:28:42.693 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:899 - ▶ [环境检查] 临时目录: /var/folders/qs/761__31103sc01c7b0m4vxth0000gp/T/
2025-07-28 13:28:42.693 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:906 - ▶ [字体文件] 开始字体文件状态检查...
2025-07-28 13:28:42.693 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:931 - [INFO] 使用本地字体目录（非JAR环境）
2025-07-28 13:28:42.693 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:939 - ▶ [字体设置] 开始字体设置验证...
2025-07-28 13:28:42.693 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:945 - [INFO] 配置的字体源数量: 2
2025-07-28 13:28:42.693 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 1: com.aspose.words.FolderFontSource@70779957 (类型: FolderFontSource)
2025-07-28 13:28:42.693 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 2: com.aspose.words.SystemFontSource@336d32a6 (类型: SystemFontSource)
2025-07-28 13:28:42.693 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:956 - [INFO] 字体替换规则状态:
2025-07-28 13:28:42.693 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:957 -   • 表格替换规则: ✓已启用
2025-07-28 13:28:42.693 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:958 -   • 默认字体替换: ✓已启用
2025-07-28 13:28:42.693 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:960 -   • 默认字体: NotoSansSC-VF.ttf
2025-07-28 13:28:42.693 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1390 - [INFO] 中文字体可用性检查:
2025-07-28 13:28:42.693 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1411 -   • NotoSansSC-VF - ✓可用 (直接字体文件)
2025-07-28 13:28:42.694 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1406 -   • SimSun - ✓可用 (替换为: NotoSansSC-VF.ttf)
2025-07-28 13:28:42.694 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1406 -   • Microsoft YaHei - ✓可用 (替换为: HYZhongHeiTi-197.ttf)
2025-07-28 13:28:42.694 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1411 -   • HYZhongHeiTi-197 - ✓可用 (直接字体文件)
2025-07-28 13:28:42.694 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:886 - ============================================================
2025-07-28 13:28:42.694 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:887 - ========== 强制字体系统诊断完成 ==========
2025-07-28 13:28:42.694 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:888 - 完成时间: 2025-07-28 13:28:42
2025-07-28 13:28:42.694 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:889 - ============================================================
2025-07-28 13:28:42.694 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1030 - ▶ [文档加载] 开始加载文档: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 13:28:43.792 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1032 - ✓ [文档加载] 文档加载成功，开始应用字体设置
2025-07-28 13:28:43.792 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1036 - ✓ [字体设置] 全局字体设置已应用到文档
2025-07-28 13:28:43.792 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1085 - ▶ [字体分析] 开始分析文档字体使用情况: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 13:28:43.792 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1092 - [INFO] 文档字体源配置: 2 个字体源
2025-07-28 13:28:43.792 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1096 -   字体源 1: FolderFontSource
2025-07-28 13:28:43.792 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1096 -   字体源 2: SystemFontSource
2025-07-28 13:28:43.861 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1126 - [INFO] 文档中使用的字体: 宋体, 仿宋, Times New Roman, Wingdings 2
2025-07-28 13:28:43.861 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1134 - [INFO] 包含中文字体: 是
2025-07-28 13:28:43.861 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1103 - ✓ [字体分析] 文档字体使用情况分析完成
2025-07-28 13:28:43.861 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1047 - ▶ [转换执行] 开始执行docx转html转换...
2025-07-28 13:28:43.862 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:996 - ▶ [HTML转换] 开始HTML转换配置:
2025-07-28 13:28:43.862 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:997 -   • 图片处理方式: Base64嵌入 (ExportImagesAsBase64=true)
2025-07-28 13:28:43.862 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:998 -   • 字体资源导出: 启用
2025-07-28 13:28:43.862 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:999 -   • 格式化输出: 启用
2025-07-28 13:28:49.613 ERROR c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1069 - ✗ [转换失败] docx转html转换失败: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx - 错误详情: Font file cannot be written to disk. When saving to a stream or to a string either FontsFolder should be specified, or custom streams should be provided via FontSavingCallback, or ExportFontsAsBase64 should be set to true. Please see documentation for details.
java.lang.IllegalStateException: Font file cannot be written to disk. When saving to a stream or to a string either FontsFolder should be specified, or custom streams should be provided via FontSavingCallback, or ExportFontsAsBase64 should be set to true. Please see documentation for details.
	at com.aspose.words.zzVWC.zzW4y(Unknown Source)
	at com.aspose.words.zzXnj.zzXXU(Unknown Source)
	at com.aspose.words.zzZlh.zzX4V(Unknown Source)
	at com.aspose.words.zzZlh.zzrF(Unknown Source)
	at com.aspose.words.zzXfB.zzZ2u(Unknown Source)
	at com.aspose.words.zzXCv.zzZ2u(Unknown Source)
	at com.aspose.words.Document.zz0l(Unknown Source)
	at com.aspose.words.Document.zzZ2u(Unknown Source)
	at com.aspose.words.Document.zzZ2u(Unknown Source)
	at com.aspose.words.Document.save(Unknown Source)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.lambda$executeDocxToHtmlConversion$16(AsposeConvertServiceImpl.java:1002)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.executeConversion(AsposeConvertServiceImpl.java:1048)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.executeDocxToHtmlConversion(AsposeConvertServiceImpl.java:983)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertRuleImpl.executeConvertRule(AsposeConvertRuleImpl.java:85)
	at com.jd.jdt.joylaw.ai.mcp.controller.AsposeConvertControlle.officeConvert(AsposeConvertControlle.java:41)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onComplete(FluxPeekFuseable.java:277)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:889)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:798)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-28 13:28:49.615 ERROR c.j.j.j.a.m.s.i.AsposeConvertRuleImpl:91 - 规则引擎执行失败: docx转html转换失败: Font file cannot be written to disk. When saving to a stream or to a string either FontsFolder should be specified, or custom streams should be provided via FontSavingCallback, or ExportFontsAsBase64 should be set to true. Please see documentation for details.
java.lang.RuntimeException: docx转html转换失败: Font file cannot be written to disk. When saving to a stream or to a string either FontsFolder should be specified, or custom streams should be provided via FontSavingCallback, or ExportFontsAsBase64 should be set to true. Please see documentation for details.
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.executeConversion(AsposeConvertServiceImpl.java:1077)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.executeDocxToHtmlConversion(AsposeConvertServiceImpl.java:983)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertRuleImpl.executeConvertRule(AsposeConvertRuleImpl.java:85)
	at com.jd.jdt.joylaw.ai.mcp.controller.AsposeConvertControlle.officeConvert(AsposeConvertControlle.java:41)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onComplete(FluxPeekFuseable.java:277)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:889)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:798)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.IllegalStateException: Font file cannot be written to disk. When saving to a stream or to a string either FontsFolder should be specified, or custom streams should be provided via FontSavingCallback, or ExportFontsAsBase64 should be set to true. Please see documentation for details.
	at com.aspose.words.zzVWC.zzW4y(Unknown Source)
	at com.aspose.words.zzXnj.zzXXU(Unknown Source)
	at com.aspose.words.zzZlh.zzX4V(Unknown Source)
	at com.aspose.words.zzZlh.zzrF(Unknown Source)
	at com.aspose.words.zzXfB.zzZ2u(Unknown Source)
	at com.aspose.words.zzXCv.zzZ2u(Unknown Source)
	at com.aspose.words.Document.zz0l(Unknown Source)
	at com.aspose.words.Document.zzZ2u(Unknown Source)
	at com.aspose.words.Document.zzZ2u(Unknown Source)
	at com.aspose.words.Document.save(Unknown Source)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.lambda$executeDocxToHtmlConversion$16(AsposeConvertServiceImpl.java:1002)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.executeConversion(AsposeConvertServiceImpl.java:1048)
	... 70 common frames omitted
2025-07-28 13:28:49.630 ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler:102 - [afdfbc42-4]  500 Server Error for HTTP POST "/api/v1/aspose-convert/word"
java.io.IOException: 规则引擎执行失败: docx转html转换失败: Font file cannot be written to disk. When saving to a stream or to a string either FontsFolder should be specified, or custom streams should be provided via FontSavingCallback, or ExportFontsAsBase64 should be set to true. Please see documentation for details.
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertRuleImpl.executeConvertRule(AsposeConvertRuleImpl.java:93)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.MonoFlatMap] :
	reactor.core.publisher.Mono.flatMap(Mono.java:3179)
	org.springframework.web.reactive.result.method.InvocableHandlerMethod.invoke(InvocableHandlerMethod.java:189)
Error has been observed at the following site(s):
	*________Mono.flatMap ⇢ at org.springframework.web.reactive.result.method.InvocableHandlerMethod.invoke(InvocableHandlerMethod.java:189)
	*__________Mono.defer ⇢ at org.springframework.web.reactive.result.method.annotation.RequestMappingHandlerAdapter.handle(RequestMappingHandlerAdapter.java:283)
	*___________Mono.then ⇢ at org.springframework.web.reactive.result.method.annotation.RequestMappingHandlerAdapter.handle(RequestMappingHandlerAdapter.java:283)
	|_      Mono.doOnNext ⇢ at org.springframework.web.reactive.result.method.annotation.RequestMappingHandlerAdapter.handle(RequestMappingHandlerAdapter.java:284)
	|_ Mono.onErrorResume ⇢ at org.springframework.web.reactive.result.method.annotation.RequestMappingHandlerAdapter.handle(RequestMappingHandlerAdapter.java:285)
	|_ Mono.onErrorResume ⇢ at org.springframework.web.reactive.DispatcherHandler.handleResultMono(DispatcherHandler.java:168)
	|_       Mono.flatMap ⇢ at org.springframework.web.reactive.DispatcherHandler.handleResultMono(DispatcherHandler.java:172)
	*__________Mono.error ⇢ at org.springframework.web.reactive.result.method.annotation.RequestMappingHandlerAdapter.handleException(RequestMappingHandlerAdapter.java:340)
	*__________Mono.error ⇢ at org.springframework.web.reactive.result.method.annotation.RequestMappingHandlerAdapter.handleException(RequestMappingHandlerAdapter.java:340)
	*________Mono.flatMap ⇢ at org.springframework.web.reactive.DispatcherHandler.handle(DispatcherHandler.java:154)
	*__________Mono.defer ⇢ at org.springframework.web.server.handler.DefaultWebFilterChain.filter(DefaultWebFilterChain.java:106)
	|_     Mono.doOnError ⇢ at org.springframework.web.server.handler.ExceptionHandlingWebHandler.handle(ExceptionHandlingWebHandler.java:84)
	|_ Mono.onErrorResume ⇢ at org.springframework.web.server.handler.ExceptionHandlingWebHandler.handle(ExceptionHandlingWebHandler.java:85)
	|_     Mono.doOnError ⇢ at org.springframework.web.server.handler.ExceptionHandlingWebHandler.handle(ExceptionHandlingWebHandler.java:84)
	*__________Mono.error ⇢ at org.springframework.web.server.handler.ExceptionHandlingWebHandler$CheckpointInsertingHandler.handle(ExceptionHandlingWebHandler.java:106)
	|_         checkpoint ⇢ HTTP POST "/api/v1/aspose-convert/word" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertRuleImpl.executeConvertRule(AsposeConvertRuleImpl.java:93)
		at com.jd.jdt.joylaw.ai.mcp.controller.AsposeConvertControlle.officeConvert(AsposeConvertControlle.java:41)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
		at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.base/java.lang.reflect.Method.invoke(Method.java:569)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
		at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onComplete(FluxPeekFuseable.java:277)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:889)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:798)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: docx转html转换失败: Font file cannot be written to disk. When saving to a stream or to a string either FontsFolder should be specified, or custom streams should be provided via FontSavingCallback, or ExportFontsAsBase64 should be set to true. Please see documentation for details.
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.executeConversion(AsposeConvertServiceImpl.java:1077)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.executeDocxToHtmlConversion(AsposeConvertServiceImpl.java:983)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertRuleImpl.executeConvertRule(AsposeConvertRuleImpl.java:85)
	at com.jd.jdt.joylaw.ai.mcp.controller.AsposeConvertControlle.officeConvert(AsposeConvertControlle.java:41)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:152)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxPeekFuseable$PeekFuseableSubscriber.onComplete(FluxPeekFuseable.java:277)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onComplete(FluxOnAssembly.java:549)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:889)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:798)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.IllegalStateException: Font file cannot be written to disk. When saving to a stream or to a string either FontsFolder should be specified, or custom streams should be provided via FontSavingCallback, or ExportFontsAsBase64 should be set to true. Please see documentation for details.
	at com.aspose.words.zzVWC.zzW4y(Unknown Source)
	at com.aspose.words.zzXnj.zzXXU(Unknown Source)
	at com.aspose.words.zzZlh.zzX4V(Unknown Source)
	at com.aspose.words.zzZlh.zzrF(Unknown Source)
	at com.aspose.words.zzXfB.zzZ2u(Unknown Source)
	at com.aspose.words.zzXCv.zzZ2u(Unknown Source)
	at com.aspose.words.Document.zz0l(Unknown Source)
	at com.aspose.words.Document.zzZ2u(Unknown Source)
	at com.aspose.words.Document.zzZ2u(Unknown Source)
	at com.aspose.words.Document.save(Unknown Source)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.lambda$executeDocxToHtmlConversion$16(AsposeConvertServiceImpl.java:1002)
	at com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertServiceImpl.executeConversion(AsposeConvertServiceImpl.java:1048)
	... 70 common frames omitted
2025-07-28 13:40:59.627 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 13:41:07.341 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-28 13:41:07.372 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 24772 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-28 13:41:07.372 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-28 13:41:07.706 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=dc45cc65-8b60-3b9e-a5fd-0ca3aa608d29
2025-07-28 13:41:08.155 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-28 13:41:08.166 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.083 seconds (process running for 2.035)
2025-07-28 13:41:48.379 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:153 - ============================================================
2025-07-28 13:41:48.380 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:154 - ========== 字体系统诊断开始 ==========
2025-07-28 13:41:48.383 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:155 - 诊断时间: 2025-07-28 13:41:48
2025-07-28 13:41:48.384 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:156 - ============================================================
2025-07-28 13:41:48.384 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:174 - ▶ [环境诊断] 开始环境诊断检查...
2025-07-28 13:41:48.384 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:183 - [INFO] 运行环境信息:
2025-07-28 13:41:48.384 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:184 -   • 操作系统: Mac OS X 15.5
2025-07-28 13:41:48.384 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:185 -   • Java版本: 17.0.15 (Microsoft)
2025-07-28 13:41:48.384 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:186 -   • 工作目录: /Users/<USER>/IdeaProjects/mcp-server
2025-07-28 13:41:48.385 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:190 -   • 运行环境: 本地环境
2025-07-28 13:41:48.386 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:211 - [INFO] 临时目录状态:
2025-07-28 13:41:48.386 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:212 -   • 临时目录路径: /var/folders/qs/761__31103sc01c7b0m4vxth0000gp/T/
2025-07-28 13:41:48.386 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:213 -   • 目录存在: ✓
2025-07-28 13:41:48.386 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:214 -   • 目录可读: ✓
2025-07-28 13:41:48.386 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:215 -   • 目录可写: ✓
2025-07-28 13:41:48.394 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:223 -   • 可用空间: 155752.88 MB / 471482.08 MB
2025-07-28 13:41:48.394 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:239 - [INFO] Java字体系统属性:
2025-07-28 13:41:48.394 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • java.awt.fonts: 未设置
2025-07-28 13:41:48.394 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • sun.java2d.fontpath: 未设置
2025-07-28 13:41:48.395 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • java.awt.headless: true
2025-07-28 13:41:48.395 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • file.encoding: UTF-8
2025-07-28 13:41:48.395 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:251 -   • sun.jnu.encoding: UTF-8
2025-07-28 13:41:48.395 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:261 - [INFO] 字体相关环境变量:
2025-07-28 13:41:48.395 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FONTCONFIG_PATH: 未设置
2025-07-28 13:41:48.395 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FONTCONFIG_FILE: 未设置
2025-07-28 13:41:48.395 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:264 -   • FC_DEBUG: 未设置
2025-07-28 13:41:48.395 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:280 - 检查系统字体目录:
2025-07-28 13:41:48.395 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   /usr/share/fonts - 不存在
2025-07-28 13:41:48.395 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   /usr/local/share/fonts - 不存在
2025-07-28 13:41:48.396 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:288 -   /System/Library/Fonts - 存在 (81 个字体文件)
2025-07-28 13:41:48.397 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:288 -   /Library/Fonts - 存在 (1 个字体文件)
2025-07-28 13:41:48.397 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:290 -   C:\Windows\Fonts - 不存在
2025-07-28 13:41:48.397 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:201 - ✓ [环境诊断] 环境诊断检查完成
2025-07-28 13:41:48.397 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:309 - --- 字体文件设置开始 ---
2025-07-28 13:41:48.398 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:319 - 字体资源URL: file:/Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts, 协议: file
2025-07-28 13:41:48.398 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:326 - 本地文件系统字体路径: /Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts
2025-07-28 13:41:48.398 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:622 - --- 字体文件分析开始 ---
2025-07-28 13:41:48.400 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:660 - 字体文件统计:
2025-07-28 13:41:48.401 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ini 格式: 1 个文件
2025-07-28 13:41:48.401 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .fon 格式: 192 个文件
2025-07-28 13:41:48.401 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ttc 格式: 14 个文件
2025-07-28 13:41:48.401 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .zip 格式: 1 个文件
2025-07-28 13:41:48.401 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .ttf 格式: 146 个文件
2025-07-28 13:41:48.401 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .dat 格式: 1 个文件
2025-07-28 13:41:48.401 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:662 -   .xml 格式: 1 个文件
2025-07-28 13:41:48.401 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:664 - 发现的优先中文字体: [Dengb.ttf, SimsunExtG.ttf, NotoSerifSC-VF.ttf, Deng.ttf, NotoSansSC-VF.ttf, HYZhongHeiTi-197.ttf, Dengl.ttf]
2025-07-28 13:41:48.401 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:665 - 发现的优先英文字体: [times.ttf, consola.ttf, arial.ttf, calibri.ttf]
2025-07-28 13:41:48.401 WARN  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:670 - 发现 192 个 .fon 格式字体文件，在Linux环境下可能不兼容
2025-07-28 13:41:48.401 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:673 - --- 字体文件分析完成 ---
2025-07-28 13:41:48.401 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:344 - --- 字体文件设置完成 ---
2025-07-28 13:41:48.401 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:680 - --- 高级字体设置开始 ---
2025-07-28 13:41:48.509 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:694 - 添加字体文件夹源: /Users/<USER>/IdeaProjects/mcp-server/target/classes/fonts
2025-07-28 13:41:48.509 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:700 - 添加系统字体源
2025-07-28 13:41:48.518 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:718 - 配置字体替换规则...
2025-07-28 13:41:48.519 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:741 - 字体替换规则配置完成
2025-07-28 13:41:48.519 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:748 - 配置字体回退设置...
2025-07-28 13:41:48.520 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:755 - 已加载Noto字体回退设置
2025-07-28 13:41:48.520 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:763 - 字体回退设置配置完成
2025-07-28 13:41:48.520 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:711 - --- 高级字体设置完成 ---
2025-07-28 13:41:48.521 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:779 - --- 字体加载状态验证开始 ---
2025-07-28 13:41:48.521 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:785 - 配置的字体源数量: 2
2025-07-28 13:41:48.521 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:789 - 字体源 1: com.aspose.words.FolderFontSource@6f41274f (类型: FolderFontSource)
2025-07-28 13:41:48.521 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:793 -   字体源类型: FolderFontSource
2025-07-28 13:41:48.521 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:794 -   字体源配置完成
2025-07-28 13:41:48.521 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:789 - 字体源 2: com.aspose.words.SystemFontSource@5f953fac (类型: SystemFontSource)
2025-07-28 13:41:48.521 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:793 -   字体源类型: SystemFontSource
2025-07-28 13:41:48.521 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:794 -   字体源配置完成
2025-07-28 13:41:48.521 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:812 - 测试字体解析能力:
2025-07-28 13:41:48.521 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'SimSun' - 解析测试通过
2025-07-28 13:41:48.521 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 '宋体' - 解析测试通过
2025-07-28 13:41:48.521 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'Microsoft YaHei' - 解析测试通过
2025-07-28 13:41:48.521 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 '微软雅黑' - 解析测试通过
2025-07-28 13:41:48.521 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:820 -   测试字体 'Arial' - 解析测试通过
2025-07-28 13:41:48.521 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:804 - --- 字体加载状态验证完成 ---
2025-07-28 13:41:48.521 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1394 - ▶ [可用性验证] 开始字体可用性验证...
2025-07-28 13:41:48.521 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1412 - [INFO] 中文字体可用性检查:
2025-07-28 13:41:48.522 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1433 -   • NotoSansSC-VF - ✓可用 (直接字体文件)
2025-07-28 13:41:48.522 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1428 -   • SimSun - ✓可用 (替换为: NotoSansSC-VF.ttf)
2025-07-28 13:41:48.522 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1428 -   • Microsoft YaHei - ✓可用 (替换为: HYZhongHeiTi-197.ttf)
2025-07-28 13:41:48.522 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1433 -   • HYZhongHeiTi-197 - ✓可用 (直接字体文件)
2025-07-28 13:41:48.522 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1448 - [INFO] 字体替换规则验证:
2025-07-28 13:41:48.522 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1455 -   • 表格替换规则: ✓已启用
2025-07-28 13:41:48.522 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1458 -   • 字体信息替换: ✓已启用
2025-07-28 13:41:48.522 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1461 -   • 默认字体替换: ✓已启用
2025-07-28 13:41:48.522 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1464 -   • 默认字体: NotoSansSC-VF.ttf
2025-07-28 13:41:48.522 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1476 - [INFO] 字体文件完整性验证:
2025-07-28 13:41:48.522 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1479 -   • 使用本地字体目录，跳过临时文件验证
2025-07-28 13:41:48.522 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1405 - ✓ [可用性验证] 字体可用性验证完成
2025-07-28 13:41:48.522 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:164 - ============================================================
2025-07-28 13:41:48.522 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:165 - ========== 字体系统诊断完成 ==========
2025-07-28 13:41:48.522 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:166 - 完成时间: 2025-07-28 13:41:48
2025-07-28 13:41:48.522 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:167 - ============================================================
2025-07-28 13:41:48.523 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1046 - 开始docx转html转换: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 13:41:48.523 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:868 - ============================================================
2025-07-28 13:41:48.523 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:869 - ========== 强制字体系统诊断开始 ==========
2025-07-28 13:41:48.523 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:870 - 诊断时间: 2025-07-28 13:41:48
2025-07-28 13:41:48.523 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:871 - 字体初始化状态: 已初始化
2025-07-28 13:41:48.523 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:872 - ============================================================
2025-07-28 13:41:48.523 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:896 - ▶ [环境检查] 运行环境: 本地环境
2025-07-28 13:41:48.523 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:897 - ▶ [环境检查] 操作系统: Mac OS X
2025-07-28 13:41:48.523 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:898 - ▶ [环境检查] 工作目录: /Users/<USER>/IdeaProjects/mcp-server
2025-07-28 13:41:48.523 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:899 - ▶ [环境检查] 临时目录: /var/folders/qs/761__31103sc01c7b0m4vxth0000gp/T/
2025-07-28 13:41:48.523 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:906 - ▶ [字体文件] 开始字体文件状态检查...
2025-07-28 13:41:48.523 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:931 - [INFO] 使用本地字体目录（非JAR环境）
2025-07-28 13:41:48.523 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:939 - ▶ [字体设置] 开始字体设置验证...
2025-07-28 13:41:48.523 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:945 - [INFO] 配置的字体源数量: 2
2025-07-28 13:41:48.523 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 1: com.aspose.words.FolderFontSource@6f41274f (类型: FolderFontSource)
2025-07-28 13:41:48.523 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 2: com.aspose.words.SystemFontSource@5f953fac (类型: SystemFontSource)
2025-07-28 13:41:48.523 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:956 - [INFO] 字体替换规则状态:
2025-07-28 13:41:48.524 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:957 -   • 表格替换规则: ✓已启用
2025-07-28 13:41:48.524 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:958 -   • 默认字体替换: ✓已启用
2025-07-28 13:41:48.524 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:960 -   • 默认字体: NotoSansSC-VF.ttf
2025-07-28 13:41:48.524 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1412 - [INFO] 中文字体可用性检查:
2025-07-28 13:41:48.524 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1433 -   • NotoSansSC-VF - ✓可用 (直接字体文件)
2025-07-28 13:41:48.524 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1428 -   • SimSun - ✓可用 (替换为: NotoSansSC-VF.ttf)
2025-07-28 13:41:48.524 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1428 -   • Microsoft YaHei - ✓可用 (替换为: HYZhongHeiTi-197.ttf)
2025-07-28 13:41:48.524 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1433 -   • HYZhongHeiTi-197 - ✓可用 (直接字体文件)
2025-07-28 13:41:48.524 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:886 - ============================================================
2025-07-28 13:41:48.524 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:887 - ========== 强制字体系统诊断完成 ==========
2025-07-28 13:41:48.524 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:888 - 完成时间: 2025-07-28 13:41:48
2025-07-28 13:41:48.524 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:889 - ============================================================
2025-07-28 13:41:48.524 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1052 - ▶ [文档加载] 开始加载文档: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 13:41:49.604 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1054 - ✓ [文档加载] 文档加载成功，开始应用字体设置
2025-07-28 13:41:49.604 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1058 - ✓ [字体设置] 全局字体设置已应用到文档
2025-07-28 13:41:49.604 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1107 - ▶ [字体分析] 开始分析文档字体使用情况: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 13:41:49.605 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1114 - [INFO] 文档字体源配置: 2 个字体源
2025-07-28 13:41:49.605 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1118 -   字体源 1: FolderFontSource
2025-07-28 13:41:49.607 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1118 -   字体源 2: SystemFontSource
2025-07-28 13:41:49.668 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1148 - [INFO] 文档中使用的字体: 宋体, 仿宋, Times New Roman, Wingdings 2
2025-07-28 13:41:49.668 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1156 - [INFO] 包含中文字体: 是
2025-07-28 13:41:49.668 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1125 - ✓ [字体分析] 文档字体使用情况分析完成
2025-07-28 13:41:49.668 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1069 - ▶ [转换执行] 开始执行docx转html转换...
2025-07-28 13:41:49.669 WARN  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:991 - 🔍 [字体诊断] 当前字体配置状态:
2025-07-28 13:41:49.669 WARN  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:992 -   • ExportFontResources: false
2025-07-28 13:41:49.669 WARN  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:993 -   • FontsFolder: 
2025-07-28 13:41:49.669 WARN  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:994 -   • ExportFontsAsBase64: 检查是否支持此配置...
2025-07-28 13:41:49.669 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1000 - ✓ [字体修复] 成功设置ExportFontsAsBase64=true
2025-07-28 13:41:49.669 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1013 - ▶ [HTML转换] 最终HTML转换配置:
2025-07-28 13:41:49.669 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1014 -   • 图片处理方式: Base64嵌入 (ExportImagesAsBase64=true)
2025-07-28 13:41:49.669 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1015 -   • 字体资源导出: 禁用
2025-07-28 13:41:49.669 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1017 -   • 字体Base64处理: 启用
2025-07-28 13:41:49.669 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1021 -   • 格式化输出: 启用
2025-07-28 13:41:55.613 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1026 - ✓ [HTML转换] HTML转换配置应用完成
2025-07-28 13:41:55.614 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1071 - ✓ [转换执行] docx转html转换执行完成
2025-07-28 13:41:55.615 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1079 - ✓ [转换完成] docx转html转换成功完成:
2025-07-28 13:41:55.615 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1080 -   • 源文件: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx (输入大小: 14817280 bytes)
2025-07-28 13:41:55.615 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1081 -   • 目标文件: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.html (输出大小: 8314382 bytes)
2025-07-28 13:41:55.615 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1082 -   • 压缩比: 56.11%
2025-07-28 13:41:55.615 INFO  c.j.j.j.a.m.s.i.AsposeConvertRuleImpl:86 - 规则引擎执行成功：.docx -> .html
2025-07-28 13:43:41.586 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1046 - 开始docx转pdf转换: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 13:43:41.587 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:868 - ============================================================
2025-07-28 13:43:41.587 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:869 - ========== 强制字体系统诊断开始 ==========
2025-07-28 13:43:41.587 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:870 - 诊断时间: 2025-07-28 13:43:41
2025-07-28 13:43:41.587 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:871 - 字体初始化状态: 已初始化
2025-07-28 13:43:41.587 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:872 - ============================================================
2025-07-28 13:43:41.587 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:896 - ▶ [环境检查] 运行环境: 本地环境
2025-07-28 13:43:41.587 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:897 - ▶ [环境检查] 操作系统: Mac OS X
2025-07-28 13:43:41.587 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:898 - ▶ [环境检查] 工作目录: /Users/<USER>/IdeaProjects/mcp-server
2025-07-28 13:43:41.587 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:899 - ▶ [环境检查] 临时目录: /var/folders/qs/761__31103sc01c7b0m4vxth0000gp/T/
2025-07-28 13:43:41.587 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:906 - ▶ [字体文件] 开始字体文件状态检查...
2025-07-28 13:43:41.587 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:931 - [INFO] 使用本地字体目录（非JAR环境）
2025-07-28 13:43:41.587 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:939 - ▶ [字体设置] 开始字体设置验证...
2025-07-28 13:43:41.587 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:945 - [INFO] 配置的字体源数量: 2
2025-07-28 13:43:41.588 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 1: com.aspose.words.FolderFontSource@6f41274f (类型: FolderFontSource)
2025-07-28 13:43:41.588 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 2: com.aspose.words.SystemFontSource@5f953fac (类型: SystemFontSource)
2025-07-28 13:43:41.588 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:956 - [INFO] 字体替换规则状态:
2025-07-28 13:43:41.588 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:957 -   • 表格替换规则: ✓已启用
2025-07-28 13:43:41.588 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:958 -   • 默认字体替换: ✓已启用
2025-07-28 13:43:41.588 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:960 -   • 默认字体: NotoSansSC-VF.ttf
2025-07-28 13:43:41.588 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1412 - [INFO] 中文字体可用性检查:
2025-07-28 13:43:41.588 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1433 -   • NotoSansSC-VF - ✓可用 (直接字体文件)
2025-07-28 13:43:41.588 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1428 -   • SimSun - ✓可用 (替换为: NotoSansSC-VF.ttf)
2025-07-28 13:43:41.588 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1428 -   • Microsoft YaHei - ✓可用 (替换为: HYZhongHeiTi-197.ttf)
2025-07-28 13:43:41.588 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1433 -   • HYZhongHeiTi-197 - ✓可用 (直接字体文件)
2025-07-28 13:43:41.588 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:886 - ============================================================
2025-07-28 13:43:41.588 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:887 - ========== 强制字体系统诊断完成 ==========
2025-07-28 13:43:41.588 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:888 - 完成时间: 2025-07-28 13:43:41
2025-07-28 13:43:41.588 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:889 - ============================================================
2025-07-28 13:43:41.588 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1052 - ▶ [文档加载] 开始加载文档: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 13:43:41.802 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1054 - ✓ [文档加载] 文档加载成功，开始应用字体设置
2025-07-28 13:43:41.803 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1058 - ✓ [字体设置] 全局字体设置已应用到文档
2025-07-28 13:43:41.803 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1107 - ▶ [字体分析] 开始分析文档字体使用情况: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx
2025-07-28 13:43:41.803 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1114 - [INFO] 文档字体源配置: 2 个字体源
2025-07-28 13:43:41.803 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1118 -   字体源 1: FolderFontSource
2025-07-28 13:43:41.803 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1118 -   字体源 2: SystemFontSource
2025-07-28 13:43:41.825 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1148 - [INFO] 文档中使用的字体: 宋体, 仿宋, Times New Roman, Wingdings 2
2025-07-28 13:43:41.825 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1156 - [INFO] 包含中文字体: 是
2025-07-28 13:43:41.825 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1125 - ✓ [字体分析] 文档字体使用情况分析完成
2025-07-28 13:43:41.826 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1065 - ▶ [PDF转换] 开始PDF转换，应用字体嵌入配置...
2025-07-28 13:43:41.826 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1170 - ▶ [PDF字体] 验证PDF字体嵌入设置...
2025-07-28 13:43:41.826 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1179 - [INFO] PDF字体嵌入配置:
2025-07-28 13:43:41.826 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1180 -   • 嵌入完整字体: ✓已启用
2025-07-28 13:43:41.826 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1181 -   • 字体嵌入模式: EMBED_ALL (嵌入所有字体)
2025-07-28 13:43:41.826 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1182 -   • 文本压缩: FLATE
2025-07-28 13:43:41.826 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1069 - ▶ [转换执行] 开始执行docx转pdf转换...
2025-07-28 13:43:48.733 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1071 - ✓ [转换执行] docx转pdf转换执行完成
2025-07-28 13:43:48.737 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1079 - ✓ [转换完成] docx转pdf转换成功完成:
2025-07-28 13:43:48.737 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1080 -   • 源文件: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx (输入大小: 14817280 bytes)
2025-07-28 13:43:48.737 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1081 -   • 目标文件: 中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.pdf (输出大小: 26627709 bytes)
2025-07-28 13:43:48.739 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1082 -   • 压缩比: 179.71%
2025-07-28 13:43:48.740 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1193 - ▶ [PDF验证] 验证PDF字体嵌入结果...
2025-07-28 13:43:48.740 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1196 - [INFO] PDF文件生成统计:
2025-07-28 13:43:48.740 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1197 -   • PDF文件大小: 26003 KB
2025-07-28 13:43:48.740 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1203 -   • 包含字体信息: ✓是
2025-07-28 13:43:48.740 INFO  c.j.j.j.a.m.s.i.AsposeConvertRuleImpl:86 - 规则引擎执行成功：.docx -> .pdf
2025-07-28 13:44:39.142 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1046 - 开始docx转pdf转换: 【样例】技术服务协议模板（jd为销售方通用版）.docx
2025-07-28 13:44:39.144 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:868 - ============================================================
2025-07-28 13:44:39.144 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:869 - ========== 强制字体系统诊断开始 ==========
2025-07-28 13:44:39.144 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:870 - 诊断时间: 2025-07-28 13:44:39
2025-07-28 13:44:39.144 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:871 - 字体初始化状态: 已初始化
2025-07-28 13:44:39.144 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:872 - ============================================================
2025-07-28 13:44:39.144 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:896 - ▶ [环境检查] 运行环境: 本地环境
2025-07-28 13:44:39.144 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:897 - ▶ [环境检查] 操作系统: Mac OS X
2025-07-28 13:44:39.144 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:898 - ▶ [环境检查] 工作目录: /Users/<USER>/IdeaProjects/mcp-server
2025-07-28 13:44:39.144 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:899 - ▶ [环境检查] 临时目录: /var/folders/qs/761__31103sc01c7b0m4vxth0000gp/T/
2025-07-28 13:44:39.144 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:906 - ▶ [字体文件] 开始字体文件状态检查...
2025-07-28 13:44:39.144 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:931 - [INFO] 使用本地字体目录（非JAR环境）
2025-07-28 13:44:39.144 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:939 - ▶ [字体设置] 开始字体设置验证...
2025-07-28 13:44:39.144 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:945 - [INFO] 配置的字体源数量: 2
2025-07-28 13:44:39.145 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 1: com.aspose.words.FolderFontSource@6f41274f (类型: FolderFontSource)
2025-07-28 13:44:39.145 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:948 -   字体源 2: com.aspose.words.SystemFontSource@5f953fac (类型: SystemFontSource)
2025-07-28 13:44:39.145 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:956 - [INFO] 字体替换规则状态:
2025-07-28 13:44:39.145 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:957 -   • 表格替换规则: ✓已启用
2025-07-28 13:44:39.145 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:958 -   • 默认字体替换: ✓已启用
2025-07-28 13:44:39.145 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:960 -   • 默认字体: NotoSansSC-VF.ttf
2025-07-28 13:44:39.145 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1412 - [INFO] 中文字体可用性检查:
2025-07-28 13:44:39.145 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1433 -   • NotoSansSC-VF - ✓可用 (直接字体文件)
2025-07-28 13:44:39.145 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1428 -   • SimSun - ✓可用 (替换为: NotoSansSC-VF.ttf)
2025-07-28 13:44:39.145 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1428 -   • Microsoft YaHei - ✓可用 (替换为: HYZhongHeiTi-197.ttf)
2025-07-28 13:44:39.145 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1433 -   • HYZhongHeiTi-197 - ✓可用 (直接字体文件)
2025-07-28 13:44:39.145 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:886 - ============================================================
2025-07-28 13:44:39.145 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:887 - ========== 强制字体系统诊断完成 ==========
2025-07-28 13:44:39.145 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:888 - 完成时间: 2025-07-28 13:44:39
2025-07-28 13:44:39.145 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:889 - ============================================================
2025-07-28 13:44:39.145 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1052 - ▶ [文档加载] 开始加载文档: 【样例】技术服务协议模板（jd为销售方通用版）.docx
2025-07-28 13:44:39.186 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1054 - ✓ [文档加载] 文档加载成功，开始应用字体设置
2025-07-28 13:44:39.186 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1058 - ✓ [字体设置] 全局字体设置已应用到文档
2025-07-28 13:44:39.186 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1107 - ▶ [字体分析] 开始分析文档字体使用情况: 【样例】技术服务协议模板（jd为销售方通用版）.docx
2025-07-28 13:44:39.186 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1114 - [INFO] 文档字体源配置: 2 个字体源
2025-07-28 13:44:39.186 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1118 -   字体源 1: FolderFontSource
2025-07-28 13:44:39.186 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1118 -   字体源 2: SystemFontSource
2025-07-28 13:44:39.189 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1148 - [INFO] 文档中使用的字体: 宋体, Calibri, 仿宋, 微软雅黑
2025-07-28 13:44:39.189 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1156 - [INFO] 包含中文字体: 是
2025-07-28 13:44:39.189 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1125 - ✓ [字体分析] 文档字体使用情况分析完成
2025-07-28 13:44:39.189 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1065 - ▶ [PDF转换] 开始PDF转换，应用字体嵌入配置...
2025-07-28 13:44:39.189 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1170 - ▶ [PDF字体] 验证PDF字体嵌入设置...
2025-07-28 13:44:39.189 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1179 - [INFO] PDF字体嵌入配置:
2025-07-28 13:44:39.189 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1180 -   • 嵌入完整字体: ✓已启用
2025-07-28 13:44:39.189 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1181 -   • 字体嵌入模式: EMBED_ALL (嵌入所有字体)
2025-07-28 13:44:39.189 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1182 -   • 文本压缩: FLATE
2025-07-28 13:44:39.189 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1069 - ▶ [转换执行] 开始执行docx转pdf转换...
2025-07-28 13:44:41.049 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1071 - ✓ [转换执行] docx转pdf转换执行完成
2025-07-28 13:44:41.052 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1079 - ✓ [转换完成] docx转pdf转换成功完成:
2025-07-28 13:44:41.053 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1080 -   • 源文件: 【样例】技术服务协议模板（jd为销售方通用版）.docx (输入大小: 51205 bytes)
2025-07-28 13:44:41.053 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1081 -   • 目标文件: 【样例】技术服务协议模板（jd为销售方通用版）.pdf (输出大小: 28008590 bytes)
2025-07-28 13:44:41.053 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1082 -   • 压缩比: 54698.94%
2025-07-28 13:44:41.057 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1193 - ▶ [PDF验证] 验证PDF字体嵌入结果...
2025-07-28 13:44:41.057 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1196 - [INFO] PDF文件生成统计:
2025-07-28 13:44:41.057 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1197 -   • PDF文件大小: 27352 KB
2025-07-28 13:44:41.057 INFO  c.j.j.j.a.m.s.i.AsposeConvertServiceImpl:1203 -   • 包含字体信息: ✓是
2025-07-28 13:44:41.057 INFO  c.j.j.j.a.m.s.i.AsposeConvertRuleImpl:86 - 规则引擎执行成功：.docx -> .pdf
