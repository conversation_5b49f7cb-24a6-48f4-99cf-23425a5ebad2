2025-07-25 13:41:03.810 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-25 13:41:03.839 INFO  c.j.j.j.a.m.OfficeConvertServiceTest:53 - Starting OfficeConvertServiceTest using Java 17.0.15 with PID 62886 (started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-25 13:41:03.840 INFO  c.j.j.j.a.m.OfficeConvertServiceTest:658 - The following 1 profile is active: "local"
2025-07-25 13:41:04.201 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=dba07196-680d-32b5-9662-578efeec1693
2025-07-25 13:41:05.018 INFO  c.j.j.j.a.m.OfficeConvertServiceTest:59 - Started OfficeConvertServiceTest in 1.443 seconds (process running for 2.281)
2025-07-25 13:41:47.035 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-25 13:41:47.057 INFO  c.j.j.j.a.m.OfficeConvertServiceTest:53 - Starting OfficeConvertServiceTest using Java 17.0.15 with PID 62986 (started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-25 13:41:47.057 INFO  c.j.j.j.a.m.OfficeConvertServiceTest:658 - The following 1 profile is active: "local"
2025-07-25 13:41:47.359 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=dba07196-680d-32b5-9662-578efeec1693
2025-07-25 13:41:47.768 INFO  c.j.j.j.a.m.OfficeConvertServiceTest:59 - Started OfficeConvertServiceTest in 0.892 seconds (process running for 1.306)
2025-07-25 15:41:51.492 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-25 15:41:51.517 INFO  c.j.j.j.a.m.OfficeConvertServiceTest:53 - Starting OfficeConvertServiceTest using Java 17.0.15 with PID 79303 (started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-25 15:41:51.518 INFO  c.j.j.j.a.m.OfficeConvertServiceTest:658 - The following 1 profile is active: "local"
2025-07-25 15:41:51.897 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=d76c026a-70ee-38a3-b8fe-2b81c801dc80
2025-07-25 15:41:52.482 INFO  c.j.j.j.a.m.OfficeConvertServiceTest:59 - Started OfficeConvertServiceTest in 1.228 seconds (process running for 1.687)
2025-07-25 15:42:42.682 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-25 15:42:42.710 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:53 - Starting OfficeConvertServiceSimpleTest using Java 17.0.15 with PID 79423 (started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-25 15:42:42.711 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:658 - The following 1 profile is active: "local"
2025-07-25 15:42:43.057 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=d76c026a-70ee-38a3-b8fe-2b81c801dc80
2025-07-25 15:42:43.500 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:59 - Started OfficeConvertServiceSimpleTest in 0.978 seconds (process running for 1.378)
2025-07-25 15:42:44.599 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:37 - 创建测试Word文档成功，文件大小: 9029 bytes
2025-07-25 15:42:45.855 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:116 - Word文档已成功转换为高质量PDF，使用了完整的格式保持配置
2025-07-25 15:42:45.857 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:50 - 转换完成，耗时: 1258 ms
2025-07-25 15:42:45.857 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:51 - 转换后PDF文件大小: 16808938 bytes
2025-07-25 15:42:45.862 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:63 - 转换后的PDF文件已保存到: target/test-output/converted_20250725_154245.pdf
2025-07-25 15:42:45.862 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:70 - === 高质量Word转PDF测试完成 ===
2025-07-25 15:42:45.862 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:71 - 请检查输出文件: target/test-output/converted_20250725_154245.pdf 以验证格式保持效果
2025-07-25 15:42:45.862 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:72 - 预期效果: 字体、加粗、颜色、表格等格式应完美还原
2025-07-25 15:42:55.034 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-25 15:42:55.056 INFO  c.j.j.j.a.m.OfficeConvertServiceTest:53 - Starting OfficeConvertServiceTest using Java 17.0.15 with PID 79465 (started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-25 15:42:55.057 INFO  c.j.j.j.a.m.OfficeConvertServiceTest:658 - The following 1 profile is active: "dev"
2025-07-25 15:42:55.354 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=d76c026a-70ee-38a3-b8fe-2b81c801dc80
2025-07-25 15:42:55.886 INFO  c.j.j.j.a.m.OfficeConvertServiceTest:59 - Started OfficeConvertServiceTest in 1.001 seconds (process running for 1.447)
2025-07-25 15:42:59.083 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:116 - Word文档已成功转换为高质量PDF，使用了完整的格式保持配置
2025-07-25 15:47:38.088 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-25 15:47:38.122 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:53 - Starting OfficeConvertServiceSimpleTest using Java 17.0.15 with PID 79970 (started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-25 15:47:38.123 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:658 - The following 1 profile is active: "dev"
2025-07-25 15:47:38.440 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=d76c026a-70ee-38a3-b8fe-2b81c801dc80
2025-07-25 15:47:38.976 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:59 - Started OfficeConvertServiceSimpleTest in 1.088 seconds (process running for 1.698)
2025-07-25 15:47:40.060 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:37 - 创建测试Word文档成功，文件大小: 9029 bytes
2025-07-25 15:47:41.365 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:116 - Word文档已成功转换为高质量PDF，使用了完整的格式保持配置
2025-07-25 15:47:41.369 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:50 - 转换完成，耗时: 1309 ms
2025-07-25 15:47:41.369 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:51 - 转换后PDF文件大小: 16808938 bytes
2025-07-25 15:47:41.374 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:63 - 转换后的PDF文件已保存到: target/test-output/converted_20250725_154741.pdf
2025-07-25 15:47:41.375 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:70 - === 高质量Word转PDF测试完成 ===
2025-07-25 15:47:41.375 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:71 - 请检查输出文件: target/test-output/converted_20250725_154741.pdf 以验证格式保持效果
2025-07-25 15:47:41.375 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:72 - 预期效果: 字体、加粗、颜色、表格等格式应完美还原
2025-07-25 15:50:07.332 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-25 15:50:07.354 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:53 - Starting OfficeConvertServiceSimpleTest using Java 17.0.15 with PID 80169 (started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-25 15:50:07.354 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:658 - The following 1 profile is active: "dev"
2025-07-25 15:50:07.654 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=d76c026a-70ee-38a3-b8fe-2b81c801dc80
2025-07-25 15:50:08.132 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:59 - Started OfficeConvertServiceSimpleTest in 0.958 seconds (process running for 1.489)
2025-07-25 15:50:08.489 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:48 - 创建测试Word文档成功，文件大小: 51205 bytes
2025-07-25 15:50:11.109 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:116 - Word文档已成功转换为高质量PDF，使用了完整的格式保持配置
2025-07-25 15:50:11.115 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:61 - 转换完成，耗时: 2625 ms
2025-07-25 15:50:11.115 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:62 - 转换后PDF文件大小: 27405373 bytes
2025-07-25 15:50:11.127 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:74 - 转换后的PDF文件已保存到: target/test-output/converted_20250725_155011.pdf
2025-07-25 15:50:11.128 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:81 - === 高质量Word转PDF测试完成 ===
2025-07-25 15:50:11.128 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:82 - 请检查输出文件: target/test-output/converted_20250725_155011.pdf 以验证格式保持效果
2025-07-25 15:50:11.128 INFO  c.j.j.j.a.m.OfficeConvertServiceSimpleTest:83 - 预期效果: 字体、加粗、颜色、表格等格式应完美还原
2025-07-25 21:54:50.748 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-25 21:54:50.772 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:53 - Starting OfficeConvertHighQualityTest using Java 17.0.15 with PID 20503 (started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-25 21:54:50.772 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:658 - The following 1 profile is active: "local"
2025-07-25 21:54:51.078 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=d76c026a-70ee-38a3-b8fe-2b81c801dc80
2025-07-25 21:54:51.559 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:59 - Started OfficeConvertHighQualityTest in 0.999 seconds (process running for 1.508)
2025-07-25 21:54:51.917 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:42 - 读取Word文件成功，文件大小: 51205 bytes
2025-07-25 21:54:52.330 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:75 - 开始高质量PDF转换，配置字体嵌入和格式保持选项
2025-07-25 21:54:52.334 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:81 - PdfSaveOptions类信息: com.aspose.words.PdfSaveOptions
2025-07-25 21:54:52.334 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:82 - 检查可用的字体嵌入相关方法...
2025-07-25 21:54:52.335 WARN  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:90 - 跳过setEmbedStandardWindowsFonts()方法调用 - 破解版20.12中不支持此方法
2025-07-25 21:54:52.335 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:148 - 配置字体替换规则以支持中文字体
2025-07-25 21:54:52.339 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:172 - 字体替换规则配置完成
2025-07-25 21:54:52.339 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:135 - PDF转换选项配置完成，开始执行转换
2025-07-25 21:54:53.387 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:140 - 高质量PDF转换完成，输出大小: 40428211 bytes
2025-07-25 21:54:53.393 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:55 - 转换完成，耗时: 1475 ms
2025-07-25 21:54:53.393 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:56 - 转换后PDF文件大小: 40428211 bytes
2025-07-25 21:54:53.407 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:68 - 高质量PDF文件已保存到: src/main/resources/static/20250725_215453.pdf
2025-07-25 21:54:53.408 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:75 - === 高质量转换测试完成 ===
2025-07-25 21:54:53.408 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:76 - 请检查输出文件: src/main/resources/static/20250725_215453.pdf 以验证格式保持效果
2025-07-25 21:54:53.408 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:77 - 预期效果: 字体、加粗、颜色、表格边框等格式应完美还原
2025-07-25 21:57:51.617 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-25 21:57:51.663 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:53 - Starting OfficeConvertHighQualityTest using Java 17.0.15 with PID 21458 (started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-25 21:57:51.664 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:658 - The following 1 profile is active: "local"
2025-07-25 21:57:52.069 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=d76c026a-70ee-38a3-b8fe-2b81c801dc80
2025-07-25 21:57:52.602 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:59 - Started OfficeConvertHighQualityTest in 1.184 seconds (process running for 1.677)
2025-07-25 21:57:53.345 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:75 - 开始高质量PDF转换，配置字体嵌入和格式保持选项
2025-07-25 21:57:53.349 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:81 - PdfSaveOptions类信息: com.aspose.words.PdfSaveOptions
2025-07-25 21:57:53.349 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:82 - 检查可用的字体嵌入相关方法...
2025-07-25 21:57:53.349 WARN  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:90 - 跳过setEmbedStandardWindowsFonts()方法调用 - 破解版20.12中不支持此方法
2025-07-25 21:57:53.349 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:148 - 配置字体替换规则以支持中文字体
2025-07-25 21:57:53.353 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:172 - 字体替换规则配置完成
2025-07-25 21:57:53.353 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:135 - PDF转换选项配置完成，开始执行转换
2025-07-25 21:57:54.236 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:140 - 高质量PDF转换完成，输出大小: 40428211 bytes
2025-07-25 21:57:54.249 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:111 - 质量对比PDF文件已保存到: src/main/resources/static/20250725_2157542.pdf
2025-07-25 21:57:54.250 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:114 - === 质量对比测试完成 ===
2025-07-25 21:57:54.250 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:115 - 请将此文件与原Word文档进行对比，检查以下格式保持情况:
2025-07-25 21:57:54.250 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:116 - 1. 字体类型和大小
2025-07-25 21:57:54.250 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:117 - 2. 文字加粗、斜体、下划线
2025-07-25 21:57:54.250 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:118 - 3. 文字颜色和背景色
2025-07-25 21:57:54.250 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:119 - 4. 表格边框和单元格样式
2025-07-25 21:57:54.250 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:120 - 5. 段落间距和行距
2025-07-25 21:57:54.250 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:121 - 6. 页眉页脚
2025-07-25 21:57:54.250 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:122 - 7. 图片质量和位置
2025-07-25 21:58:06.921 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-25 21:58:06.946 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:53 - Starting OfficeConvertHighQualityTest using Java 17.0.15 with PID 21569 (started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-25 21:58:06.947 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:658 - The following 1 profile is active: "local"
2025-07-25 21:58:07.249 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=d76c026a-70ee-38a3-b8fe-2b81c801dc80
2025-07-25 21:58:07.747 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:59 - Started OfficeConvertHighQualityTest in 0.996 seconds (process running for 1.458)
2025-07-25 21:58:08.454 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:75 - 开始高质量PDF转换，配置字体嵌入和格式保持选项
2025-07-25 21:58:08.458 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:81 - PdfSaveOptions类信息: com.aspose.words.PdfSaveOptions
2025-07-25 21:58:08.458 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:82 - 检查可用的字体嵌入相关方法...
2025-07-25 21:58:08.458 WARN  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:90 - 跳过setEmbedStandardWindowsFonts()方法调用 - 破解版20.12中不支持此方法
2025-07-25 21:58:08.458 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:148 - 配置字体替换规则以支持中文字体
2025-07-25 21:58:08.462 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:172 - 字体替换规则配置完成
2025-07-25 21:58:08.462 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:135 - PDF转换选项配置完成，开始执行转换
2025-07-25 21:58:09.327 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:140 - 高质量PDF转换完成，输出大小: 40428211 bytes
2025-07-25 21:58:09.351 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:111 - 质量对比PDF文件已保存到: src/main/resources/static/20250725_2158092.pdf
2025-07-25 21:58:09.351 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:114 - === 质量对比测试完成 ===
2025-07-25 21:58:09.352 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:115 - 请将此文件与原Word文档进行对比，检查以下格式保持情况:
2025-07-25 21:58:09.352 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:116 - 1. 字体类型和大小
2025-07-25 21:58:09.352 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:117 - 2. 文字加粗、斜体、下划线
2025-07-25 21:58:09.352 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:118 - 3. 文字颜色和背景色
2025-07-25 21:58:09.352 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:119 - 4. 表格边框和单元格样式
2025-07-25 21:58:09.352 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:120 - 5. 段落间距和行距
2025-07-25 21:58:09.352 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:121 - 6. 页眉页脚
2025-07-25 21:58:09.352 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:122 - 7. 图片质量和位置
2025-07-25 22:09:44.335 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-25 22:09:44.365 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:53 - Starting OfficeConvertUltimateQualityTest using Java 17.0.15 with PID 25900 (started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-25 22:09:44.366 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:658 - The following 1 profile is active: "local"
2025-07-25 22:09:44.646 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=d76c026a-70ee-38a3-b8fe-2b81c801dc80
2025-07-25 22:09:46.054 INFO  o.s.c.c.u.InetUtils:170 - Cannot determine local hostname
2025-07-25 22:09:46.308 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:59 - Started OfficeConvertUltimateQualityTest in 3.147 seconds (process running for 3.58)
2025-07-25 22:09:46.680 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:32 - === 开始终极质量Word转PDF转换测试 ===
2025-07-25 22:09:46.682 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:44 - ✓ 读取Word文件成功
2025-07-25 22:09:46.684 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:45 -   - 文件路径: src/main/resources/static/【样例】技术服务协议模板（jd为销售方通用版）.docx
2025-07-25 22:09:46.684 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:46 -   - 文件大小: 51205 bytes (50 KB)
2025-07-25 22:09:46.684 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:55 - 开始执行终极质量Word转PDF转换...
2025-07-25 22:09:46.684 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:39 - 开始文档转换: 【样例】技术服务协议模板（jd为销售方通用版）.docx -> DOCX_TO_PDF
2025-07-25 22:09:47.611 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:45 - 文档加载成功，页数: 16, 节数: 1
2025-07-25 22:09:47.611 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:83 - === 开始终极质量PDF转换 ===
2025-07-25 22:09:47.611 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:113 - 开始文档预处理...
2025-07-25 22:09:47.808 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:128 - 文档预处理完成
2025-07-25 22:09:47.809 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:200 - 配置高级字体处理...
2025-07-25 22:09:47.809 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:217 - 高级字体处理配置完成
2025-07-25 22:09:47.809 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:306 - 创建终极PDF保存选项...
2025-07-25 22:09:47.809 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:352 - 终极PDF保存选项创建完成
2025-07-25 22:09:47.810 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:360 - 执行转换前检查...
2025-07-25 22:09:47.810 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:364 - 文档页数: 16
2025-07-25 22:09:47.811 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:401 - 文档使用的字体: [宋体, Calibri, 仿宋, 微软雅黑]
2025-07-25 22:09:47.811 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:406 - 检测到中文宋体字体: 宋体
2025-07-25 22:09:47.812 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:409 - 检测到微软雅黑字体: 微软雅黑
2025-07-25 22:09:47.812 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:375 - 文档图片数量: 0
2025-07-25 22:09:47.812 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:379 - 文档表格数量: 3
2025-07-25 22:09:47.812 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:381 - 转换前检查完成
2025-07-25 22:09:48.478 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:102 - === 终极质量PDF转换完成 ===
2025-07-25 22:09:48.479 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:103 - 转换耗时: 666 ms, 输出大小: 61160347 bytes
2025-07-25 22:09:48.479 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:418 - 执行转换后验证...
2025-07-25 22:09:48.479 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:425 - PDF输出大小正常: 61160347 bytes
2025-07-25 22:09:48.483 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:433 - PDF格式验证通过
2025-07-25 22:09:48.484 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:439 - 转换后验证完成
2025-07-25 22:09:48.491 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:68 - 转换完成: 【样例】技术服务协议模板（jd为销售方通用版）.docx -> 【样例】技术服务协议模板（jd为销售方通用版）.pdf, 输出大小: 61160347 bytes
2025-07-25 22:09:48.491 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:63 - ✓ 终极质量转换完成！
2025-07-25 22:09:48.492 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:64 - === 转换统计信息 ===
2025-07-25 22:09:48.492 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:65 -   - 转换耗时: 1807 ms ({:.2f} 秒)
2025-07-25 22:09:48.492 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:66 -   - 原文件大小: 51205 bytes (50 KB)
2025-07-25 22:09:48.492 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:67 -   - PDF文件大小: 61160347 bytes (59726 KB)
2025-07-25 22:09:48.492 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:68 -   - 文件大小比率: {:.2f}%
2025-07-25 22:09:48.492 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:69 -   - 转换速度: {:.2f} KB/s
2025-07-25 22:09:48.516 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:81 - ✓ 终极质量PDF文件已保存到: target/test-output/ultimate_quality_20250725_220948.pdf
2025-07-25 22:09:48.516 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:207 - === 执行详细验证 ===
2025-07-25 22:09:48.517 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:219 - ✓ PDF格式验证通过
2025-07-25 22:11:05.193 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-25 22:11:05.213 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:53 - Starting OfficeConvertUltimateQualityTest using Java 17.0.15 with PID 26404 (started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-25 22:11:05.214 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:658 - The following 1 profile is active: "local"
2025-07-25 22:11:05.477 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=d76c026a-70ee-38a3-b8fe-2b81c801dc80
2025-07-25 22:11:05.937 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:59 - Started OfficeConvertUltimateQualityTest in 0.908 seconds (process running for 1.284)
2025-07-25 22:11:06.269 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:32 - === 开始终极质量Word转PDF转换测试 ===
2025-07-25 22:11:06.271 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:44 - ✓ 读取Word文件成功
2025-07-25 22:11:06.273 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:45 -   - 文件路径: src/main/resources/static/【样例】技术服务协议模板（jd为销售方通用版）.docx
2025-07-25 22:11:06.273 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:46 -   - 文件大小: 51205 bytes (50 KB)
2025-07-25 22:11:06.273 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:55 - 开始执行终极质量Word转PDF转换...
2025-07-25 22:11:06.273 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:39 - 开始文档转换: 【样例】技术服务协议模板（jd为销售方通用版）.docx -> DOCX_TO_PDF
2025-07-25 22:11:07.139 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:45 - 文档加载成功，页数: 16, 节数: 1
2025-07-25 22:11:07.140 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:83 - === 开始终极质量PDF转换 ===
2025-07-25 22:11:07.140 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:113 - 开始文档预处理...
2025-07-25 22:11:07.328 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:128 - 文档预处理完成
2025-07-25 22:11:07.328 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:200 - 配置高级字体处理...
2025-07-25 22:11:07.329 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:217 - 高级字体处理配置完成
2025-07-25 22:11:07.329 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:306 - 创建终极PDF保存选项...
2025-07-25 22:11:07.329 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:352 - 终极PDF保存选项创建完成
2025-07-25 22:11:07.329 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:360 - 执行转换前检查...
2025-07-25 22:11:07.329 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:364 - 文档页数: 16
2025-07-25 22:11:07.330 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:401 - 文档使用的字体: [宋体, Calibri, 仿宋, 微软雅黑]
2025-07-25 22:11:07.330 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:406 - 检测到中文宋体字体: 宋体
2025-07-25 22:11:07.330 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:409 - 检测到微软雅黑字体: 微软雅黑
2025-07-25 22:11:07.330 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:375 - 文档图片数量: 0
2025-07-25 22:11:07.331 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:379 - 文档表格数量: 3
2025-07-25 22:11:07.331 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:381 - 转换前检查完成
2025-07-25 22:11:07.665 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:102 - === 终极质量PDF转换完成 ===
2025-07-25 22:11:07.665 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:103 - 转换耗时: 334 ms, 输出大小: 361556 bytes
2025-07-25 22:11:07.665 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:418 - 执行转换后验证...
2025-07-25 22:11:07.666 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:425 - PDF输出大小正常: 361556 bytes
2025-07-25 22:11:07.666 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:433 - PDF格式验证通过
2025-07-25 22:11:07.666 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:439 - 转换后验证完成
2025-07-25 22:11:07.666 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:68 - 转换完成: 【样例】技术服务协议模板（jd为销售方通用版）.docx -> 【样例】技术服务协议模板（jd为销售方通用版）.pdf, 输出大小: 361556 bytes
2025-07-25 22:11:07.666 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:63 - ✓ 终极质量转换完成！
2025-07-25 22:11:07.666 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:64 - === 转换统计信息 ===
2025-07-25 22:11:07.666 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:65 -   - 转换耗时: 1393 ms ({:.2f} 秒)
2025-07-25 22:11:07.666 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:66 -   - 原文件大小: 51205 bytes (50 KB)
2025-07-25 22:11:07.667 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:67 -   - PDF文件大小: 361556 bytes (353 KB)
2025-07-25 22:11:07.667 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:68 -   - 文件大小比率: {:.2f}%
2025-07-25 22:11:07.667 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:69 -   - 转换速度: {:.2f} KB/s
2025-07-25 22:11:07.667 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:81 - ✓ 终极质量PDF文件已保存到: target/test-output/ultimate_quality_20250725_221107.pdf
2025-07-25 22:11:07.667 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:207 - === 执行详细验证 ===
2025-07-25 22:11:07.668 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:219 - ✓ PDF格式验证通过
2025-07-25 22:11:07.668 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:226 - ✓ 文件大小验证通过: 361556 bytes
2025-07-25 22:11:07.668 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:231 - ✓ 文件可读性验证通过
2025-07-25 22:11:07.668 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:233 - === 详细验证完成 ===
2025-07-25 22:11:07.668 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:87 - === 终极质量转换测试完成 ===
2025-07-25 22:11:07.668 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:88 - 请检查输出文件: target/test-output/ultimate_quality_20250725_221107.pdf 以验证格式保持效果
2025-07-25 22:11:07.668 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:292 - 
2025-07-25 22:11:07.668 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:293 - === 终极质量PDF检查清单 ===
2025-07-25 22:11:07.668 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:294 - 请手动验证以下格式保持情况（深度优化版）:
2025-07-25 22:11:07.668 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:295 - 
2025-07-25 22:11:07.668 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:296 - 【字体质量检查】
2025-07-25 22:11:07.668 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:297 - □ 1. 中文字体完美显示（宋体、黑体、微软雅黑、楷体、仿宋）
2025-07-25 22:11:07.669 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:298 - □ 2. 英文字体完美显示（Times New Roman、Arial、Calibri、Cambria）
2025-07-25 22:11:07.669 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:299 - □ 3. 字体大小精确保持，无缩放变形
2025-07-25 22:11:07.669 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:300 - □ 4. 字体粗细（加粗、正常）完全一致
2025-07-25 22:11:07.669 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:301 - □ 5. 字体样式（斜体、下划线）完全保持
2025-07-25 22:11:07.669 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:302 - 
2025-07-25 22:11:07.669 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:303 - 【格式质量检查】
2025-07-25 22:11:07.669 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:304 - □ 6. 文字颜色和背景色精确还原
2025-07-25 22:11:07.669 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:305 - □ 7. 段落间距和行距完全一致
2025-07-25 22:11:07.669 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:306 - □ 8. 文本对齐方式（左对齐、居中、右对齐、两端对齐）保持
2025-07-25 22:11:07.669 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:307 - □ 9. 首行缩进和段落缩进精确保持
2025-07-25 22:11:07.669 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:308 - □ 10. 项目符号和编号格式完整
2025-07-25 22:11:07.669 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:309 - 
2025-07-25 22:11:07.669 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:310 - 【表格质量检查】
2025-07-25 22:11:07.669 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:311 - □ 11. 表格边框线条粗细和颜色正确
2025-07-25 22:11:07.670 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:312 - □ 12. 单元格背景色和文字颜色保持
2025-07-25 22:11:07.670 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:313 - □ 13. 表格列宽和行高精确保持
2025-07-25 22:11:07.670 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:314 - □ 14. 单元格内文字对齐方式正确
2025-07-25 22:11:07.670 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:315 - □ 15. 合并单元格格式完整
2025-07-25 22:11:07.670 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:316 - 
2025-07-25 22:11:07.670 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:317 - 【页面布局检查】
2025-07-25 22:11:07.670 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:318 - □ 16. 页面边距完全一致
2025-07-25 22:11:07.670 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:319 - □ 17. 页眉页脚内容和格式完整
2025-07-25 22:11:07.670 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:320 - □ 18. 页码位置和格式正确
2025-07-25 22:11:07.670 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:321 - □ 19. 分页位置准确，无异常分页
2025-07-25 22:11:07.670 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:322 - □ 20. 页面方向（横向/纵向）保持
2025-07-25 22:11:07.670 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:323 - 
2025-07-25 22:11:07.670 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:324 - 【图像质量检查】
2025-07-25 22:11:07.670 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:325 - □ 21. 图片清晰度高，无模糊
2025-07-25 22:11:07.671 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:326 - □ 22. 图片位置和大小精确
2025-07-25 22:11:07.671 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:327 - □ 23. 图片环绕文字方式保持
2025-07-25 22:11:07.671 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:328 - □ 24. 图片颜色和对比度正确
2025-07-25 22:11:07.671 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:329 - 
2025-07-25 22:11:07.671 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:330 - 【功能性检查】
2025-07-25 22:11:07.671 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:331 - □ 25. 超链接可点击且跳转正确
2025-07-25 22:11:07.671 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:332 - □ 26. 书签和目录结构完整
2025-07-25 22:11:07.671 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:333 - □ 27. 交叉引用功能正常
2025-07-25 22:11:07.671 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:334 - □ 28. 脚注和尾注格式正确
2025-07-25 22:11:07.671 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:335 - 
2025-07-25 22:11:07.671 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:336 - 【整体质量检查】
2025-07-25 22:11:07.671 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:337 - □ 29. 整体视觉效果与Word文档100%一致
2025-07-25 22:11:07.671 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:338 - □ 30. 无任何格式丢失或变形
2025-07-25 22:11:07.671 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:339 - □ 31. 文档结构完整，无缺失内容
2025-07-25 22:11:07.671 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:340 - □ 32. PDF文件大小合理，质量优秀
2025-07-25 22:11:07.671 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:341 - 
2025-07-25 22:11:07.672 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:342 - 如果以上所有项目都符合预期，说明终极质量格式保持修复成功！
2025-07-25 22:11:07.672 INFO  c.j.j.j.a.m.OfficeConvertUltimateQualityTest:343 - === 检查清单结束 ===
2025-07-25 22:14:50.328 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-25 22:14:50.349 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:53 - Starting OfficeConvertHighQualityTest using Java 17.0.15 with PID 28321 (started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-25 22:14:50.350 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:658 - The following 1 profile is active: "local"
2025-07-25 22:14:50.658 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=d76c026a-70ee-38a3-b8fe-2b81c801dc80
2025-07-25 22:14:51.136 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:59 - Started OfficeConvertHighQualityTest in 0.975 seconds (process running for 1.41)
2025-07-25 22:14:51.476 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:42 - 读取Word文件成功，文件大小: 51205 bytes
2025-07-25 22:14:51.477 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:39 - 开始文档转换: 【样例】技术服务协议模板（jd为销售方通用版）.docx -> DOCX_TO_PDF
2025-07-25 22:14:52.320 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:45 - 文档加载成功，页数: 16, 节数: 1
2025-07-25 22:14:52.321 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:83 - === 开始终极质量PDF转换 ===
2025-07-25 22:14:52.321 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:113 - 开始文档预处理...
2025-07-25 22:14:52.515 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:128 - 文档预处理完成
2025-07-25 22:14:52.515 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:200 - 配置高级字体处理...
2025-07-25 22:14:52.516 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:217 - 高级字体处理配置完成
2025-07-25 22:14:52.516 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:306 - 创建终极PDF保存选项...
2025-07-25 22:14:52.516 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:352 - 终极PDF保存选项创建完成
2025-07-25 22:14:52.516 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:360 - 执行转换前检查...
2025-07-25 22:14:52.516 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:364 - 文档页数: 16
2025-07-25 22:14:52.517 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:401 - 文档使用的字体: [宋体, Calibri, 仿宋, 微软雅黑]
2025-07-25 22:14:52.517 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:406 - 检测到中文宋体字体: 宋体
2025-07-25 22:14:52.517 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:409 - 检测到微软雅黑字体: 微软雅黑
2025-07-25 22:14:52.517 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:375 - 文档图片数量: 0
2025-07-25 22:14:52.518 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:379 - 文档表格数量: 3
2025-07-25 22:14:52.518 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:381 - 转换前检查完成
2025-07-25 22:14:52.855 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:102 - === 终极质量PDF转换完成 ===
2025-07-25 22:14:52.856 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:103 - 转换耗时: 337 ms, 输出大小: 361556 bytes
2025-07-25 22:14:52.856 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:418 - 执行转换后验证...
2025-07-25 22:14:52.856 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:425 - PDF输出大小正常: 361556 bytes
2025-07-25 22:14:52.856 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:433 - PDF格式验证通过
2025-07-25 22:14:52.856 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:439 - 转换后验证完成
2025-07-25 22:14:52.856 INFO  c.j.j.j.a.m.s.i.OfficeConvertServiceImpl:68 - 转换完成: 【样例】技术服务协议模板（jd为销售方通用版）.docx -> 【样例】技术服务协议模板（jd为销售方通用版）.pdf, 输出大小: 361556 bytes
2025-07-25 22:14:52.856 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:55 - 转换完成，耗时: 1379 ms
2025-07-25 22:14:52.856 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:56 - 转换后PDF文件大小: 361556 bytes
2025-07-25 22:14:52.857 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:68 - 高质量PDF文件已保存到: src/main/resources/static/20250725_221452.pdf
2025-07-25 22:14:52.857 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:75 - === 高质量转换测试完成 ===
2025-07-25 22:14:52.857 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:76 - 请检查输出文件: src/main/resources/static/20250725_221452.pdf 以验证格式保持效果
2025-07-25 22:14:52.857 INFO  c.j.j.j.a.m.OfficeConvertHighQualityTest:77 - 预期效果: 字体、加粗、颜色、表格边框等格式应完美还原
