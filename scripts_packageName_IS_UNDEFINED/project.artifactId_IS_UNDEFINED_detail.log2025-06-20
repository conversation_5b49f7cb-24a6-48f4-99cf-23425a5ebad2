2025-06-20 11:12:26.418 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-20 11:12:26.449 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 46693 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by <PERSON><PERSON><PERSON><PERSON> in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-20 11:12:26.449 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-20 11:12:26.766 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=12177c01-6922-3d03-b964-32af957e7ba3
2025-06-20 11:12:27.329 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-06-20 11:12:27.336 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.136 seconds (process running for 1.653)
2025-06-20 11:13:46.608 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-20 11:13:46.612 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-20 19:32:50.792 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-20 19:32:50.833 INFO  o.s.b.w.e.n.GracefulShutdown:62 - Graceful shutdown complete
