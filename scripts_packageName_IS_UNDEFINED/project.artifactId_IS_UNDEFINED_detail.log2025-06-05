2025-06-05 10:08:56.252 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-05 10:08:56.265 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-05 10:09:29.947 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-05 10:09:29.948 WARN  i.m.s.McpAsyncServer:477 - <PERSON><PERSON> requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-05 10:45:10.614 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-05 10:45:10.616 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-05 14:17:13.247 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-05 14:17:13.259 INFO  o.s.b.w.e.n.GracefulShutdown:62 - Graceful shutdown complete
2025-06-05 14:18:22.692 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-05 14:18:22.720 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 72289 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-05 14:18:22.721 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-05 14:18:23.010 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=49fcb10f-217c-3000-ae25-22ddf26da6f7
2025-06-05 14:18:23.423 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-06-05 14:18:23.430 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.944 seconds (process running for 1.373)
2025-06-05 18:16:07.367 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-05 18:16:07.379 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
