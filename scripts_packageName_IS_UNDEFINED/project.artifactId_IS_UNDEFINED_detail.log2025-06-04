2025-06-04 15:50:23.231 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 15:50:23.256 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 59841 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by z<PERSON><PERSON><PERSON> in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 15:50:23.256 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-04 15:50:23.524 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=49fcb10f-217c-3000-ae25-22ddf26da6f7
2025-06-04 15:50:23.922 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-06-04 15:50:23.929 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.89 seconds (process running for 1.241)
2025-06-04 16:30:02.930 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-04 16:32:25.974 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 16:32:26.005 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 63819 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 16:32:26.006 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-04 16:32:26.289 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=49fcb10f-217c-3000-ae25-22ddf26da6f7
2025-06-04 16:32:26.689 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-06-04 16:32:26.695 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.904 seconds (process running for 1.33)
2025-06-04 16:32:32.587 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-04 16:32:32.591 INFO  o.s.b.w.e.n.GracefulShutdown:62 - Graceful shutdown complete
2025-06-04 16:32:35.905 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 16:32:35.930 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 63838 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 16:32:35.930 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-04 16:32:36.206 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=49fcb10f-217c-3000-ae25-22ddf26da6f7
2025-06-04 16:32:36.599 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-06-04 16:32:36.605 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.914 seconds (process running for 1.287)
2025-06-04 16:32:53.847 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-04 16:32:53.849 INFO  o.s.b.w.e.n.GracefulShutdown:62 - Graceful shutdown complete
2025-06-04 16:32:57.215 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 16:32:57.242 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 63886 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 16:32:57.242 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-04 16:32:57.509 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=49fcb10f-217c-3000-ae25-22ddf26da6f7
2025-06-04 16:32:57.902 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-06-04 16:32:57.908 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.892 seconds (process running for 1.266)
2025-06-04 16:33:03.492 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-04 16:33:03.494 INFO  o.s.b.w.e.n.GracefulShutdown:62 - Graceful shutdown complete
2025-06-04 16:33:09.666 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 16:33:09.692 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 63914 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 16:33:09.692 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-04 16:33:09.946 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=49fcb10f-217c-3000-ae25-22ddf26da6f7
2025-06-04 16:33:10.369 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-06-04 16:33:10.376 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.918 seconds (process running for 1.281)
2025-06-04 16:33:33.092 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-04 16:33:33.094 INFO  o.s.b.w.e.n.GracefulShutdown:62 - Graceful shutdown complete
2025-06-04 16:33:36.930 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 16:33:36.955 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 63966 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 16:33:36.955 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "pre"
2025-06-04 16:33:37.218 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=49fcb10f-217c-3000-ae25-22ddf26da6f7
2025-06-04 16:33:37.608 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-06-04 16:33:37.615 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.866 seconds (process running for 1.233)
2025-06-04 16:36:10.811 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-04 16:36:10.816 INFO  o.s.b.w.e.n.GracefulShutdown:62 - Graceful shutdown complete
2025-06-04 16:36:30.204 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 16:36:30.236 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 64319 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 16:36:30.236 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "pre"
2025-06-04 16:36:30.519 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=49fcb10f-217c-3000-ae25-22ddf26da6f7
2025-06-04 16:36:30.919 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-06-04 16:36:30.925 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.899 seconds (process running for 1.312)
2025-06-04 16:36:45.396 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-04 16:36:45.398 INFO  o.s.b.w.e.n.GracefulShutdown:62 - Graceful shutdown complete
2025-06-04 16:37:13.244 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 16:37:13.271 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 64403 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 16:37:13.271 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-04 16:37:13.543 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=49fcb10f-217c-3000-ae25-22ddf26da6f7
2025-06-04 16:37:13.939 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-06-04 16:37:13.945 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.922 seconds (process running for 1.311)
2025-06-04 17:48:26.633 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-04 17:48:26.648 INFO  o.s.b.w.e.n.GracefulShutdown:62 - Graceful shutdown complete
2025-06-04 17:48:47.510 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 17:48:47.549 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 70840 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 17:48:47.550 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-04 17:48:47.841 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=49fcb10f-217c-3000-ae25-22ddf26da6f7
2025-06-04 17:48:48.241 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-06-04 17:48:48.247 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.936 seconds (process running for 1.338)
2025-06-04 17:49:37.948 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 17:49:37.948 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 17:49:37.951 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 17:49:37.951 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 17:49:40.094 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 17:49:40.095 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 17:52:19.000 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 17:52:19.002 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:47:15.145 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-04 20:47:15.156 INFO  o.s.b.w.e.n.GracefulShutdown:62 - Graceful shutdown complete
2025-06-04 20:47:32.841 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 20:47:32.870 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 85812 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 20:47:32.871 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-04 20:47:33.170 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=88352819-fcfc-3ac6-b10b-b1c64831c159
2025-06-04 20:47:33.582 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-06-04 20:47:33.589 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.96 seconds (process running for 1.798)
2025-06-04 20:48:15.700 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:48:15.700 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:48:15.703 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:48:15.703 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:48:21.404 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:48:21.405 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:48:22.231 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:48:22.232 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:48:22.770 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:48:22.771 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:48:23.178 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:48:23.179 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:48:28.334 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:48:28.334 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:51:22.753 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:51:22.755 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:51:25.538 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:51:25.539 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:52:22.961 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-04 20:52:28.215 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 20:52:28.239 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 86438 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 20:52:28.240 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-04 20:52:28.507 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=88352819-fcfc-3ac6-b10b-b1c64831c159
2025-06-04 20:52:28.905 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-06-04 20:52:28.912 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.893 seconds (process running for 1.253)
2025-06-04 20:52:33.806 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:52:33.810 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:52:34.943 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:52:34.943 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:52:35.650 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:52:35.651 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:52:36.146 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:52:36.147 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:52:36.613 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:52:36.613 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:52:37.051 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:52:37.052 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:52:37.459 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:52:37.460 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:52:37.804 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:52:37.805 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:52:38.158 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:52:38.158 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:52:38.490 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:52:38.491 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:52:38.782 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:52:38.783 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:52:40.590 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:52:40.591 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:52:40.932 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:52:40.933 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:53:13.466 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 20:53:13.468 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
2025-06-04 20:59:53.065 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-04 21:02:17.238 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 21:02:17.267 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 87086 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 21:02:17.268 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-04 21:02:17.581 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=ad2b7412-5158-310d-b612-b9303e150e92
2025-06-04 21:02:17.702 INFO  o.s.b.w.e.t.TomcatWebServer:111 - Tomcat initialized with port 8081 (http)
2025-06-04 21:02:17.708 INFO  o.a.c.h.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-04 21:02:17.708 INFO  o.a.c.c.StandardService:173 - Starting service [Tomcat]
2025-06-04 21:02:17.708 INFO  o.a.c.c.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-06-04 21:02:17.731 INFO  o.a.c.c.C.[.[.[/]:173 - Initializing Spring embedded WebApplicationContext
2025-06-04 21:02:17.732 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:301 - Root WebApplicationContext: initialization completed in 448 ms
2025-06-04 21:02:18.048 INFO  o.a.c.h.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8081"]
2025-06-04 21:02:18.053 INFO  o.s.b.w.e.t.TomcatWebServer:243 - Tomcat started on port 8081 (http) with context path '/'
2025-06-04 21:02:18.061 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.029 seconds (process running for 1.428)
2025-06-04 21:02:18.840 INFO  o.a.c.c.C.[.[.[/]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 21:02:18.840 INFO  o.s.w.s.DispatcherServlet:532 - Initializing Servlet 'dispatcherServlet'
2025-06-04 21:02:18.841 INFO  o.s.w.s.DispatcherServlet:554 - Completed initialization in 1 ms
2025-06-04 21:06:16.853 INFO  o.s.b.w.e.t.GracefulShutdown:54 - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-04 21:06:16.868 INFO  o.s.b.w.e.t.GracefulShutdown:76 - Graceful shutdown complete
2025-06-04 21:06:19.258 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 21:06:19.284 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 87427 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 21:06:19.284 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-04 21:06:19.581 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=2ddc9f27-c0c3-383f-a856-a1c25e6c0faa
2025-06-04 21:06:19.690 INFO  o.s.b.w.e.t.TomcatWebServer:111 - Tomcat initialized with port 8081 (http)
2025-06-04 21:06:19.695 INFO  o.a.c.h.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-04 21:06:19.696 INFO  o.a.c.c.StandardService:173 - Starting service [Tomcat]
2025-06-04 21:06:19.696 INFO  o.a.c.c.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-06-04 21:06:19.717 INFO  o.a.c.c.C.[.[.[/]:173 - Initializing Spring embedded WebApplicationContext
2025-06-04 21:06:19.717 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:301 - Root WebApplicationContext: initialization completed in 419 ms
2025-06-04 21:06:20.030 INFO  o.a.c.h.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8081"]
2025-06-04 21:06:20.035 INFO  o.s.b.w.e.t.TomcatWebServer:243 - Tomcat started on port 8081 (http) with context path '/'
2025-06-04 21:06:20.042 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.974 seconds (process running for 1.344)
2025-06-04 21:06:27.812 INFO  o.a.c.c.C.[.[.[/]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 21:06:27.812 INFO  o.s.w.s.DispatcherServlet:532 - Initializing Servlet 'dispatcherServlet'
2025-06-04 21:06:27.814 INFO  o.s.w.s.DispatcherServlet:554 - Completed initialization in 2 ms
2025-06-04 21:06:37.964 WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver:254 - Resolved [org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' is not supported]
2025-06-04 21:06:38.684 WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver:254 - Resolved [org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' is not supported]
2025-06-04 21:07:04.510 INFO  o.s.b.w.e.t.GracefulShutdown:54 - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-04 21:07:06.912 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 21:07:06.939 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 87472 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 21:07:06.940 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-04 21:07:07.238 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=2ddc9f27-c0c3-383f-a856-a1c25e6c0faa
2025-06-04 21:07:07.363 INFO  o.s.b.w.e.t.TomcatWebServer:111 - Tomcat initialized with port 8081 (http)
2025-06-04 21:07:07.368 INFO  o.a.c.h.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-04 21:07:07.369 INFO  o.a.c.c.StandardService:173 - Starting service [Tomcat]
2025-06-04 21:07:07.369 INFO  o.a.c.c.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-06-04 21:07:07.390 INFO  o.a.c.c.C.[.[.[/]:173 - Initializing Spring embedded WebApplicationContext
2025-06-04 21:07:07.391 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:301 - Root WebApplicationContext: initialization completed in 436 ms
2025-06-04 21:07:07.700 INFO  o.a.c.h.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8081"]
2025-06-04 21:07:07.705 INFO  o.s.b.w.e.t.TomcatWebServer:243 - Tomcat started on port 8081 (http) with context path '/'
2025-06-04 21:07:07.713 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.987 seconds (process running for 1.354)
2025-06-04 21:07:08.096 INFO  o.s.b.w.e.t.GracefulShutdown:54 - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-04 21:07:08.113 INFO  o.s.b.w.e.t.GracefulShutdown:76 - Graceful shutdown complete
2025-06-04 21:07:09.438 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 21:07:09.462 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 87477 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 21:07:09.462 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-04 21:07:09.763 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=2ddc9f27-c0c3-383f-a856-a1c25e6c0faa
2025-06-04 21:07:09.875 INFO  o.s.b.w.e.t.TomcatWebServer:111 - Tomcat initialized with port 8081 (http)
2025-06-04 21:07:09.880 INFO  o.a.c.h.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-04 21:07:09.881 INFO  o.a.c.c.StandardService:173 - Starting service [Tomcat]
2025-06-04 21:07:09.881 INFO  o.a.c.c.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-06-04 21:07:09.903 INFO  o.a.c.c.C.[.[.[/]:173 - Initializing Spring embedded WebApplicationContext
2025-06-04 21:07:09.903 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:301 - Root WebApplicationContext: initialization completed in 426 ms
2025-06-04 21:07:10.203 INFO  o.a.c.h.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8081"]
2025-06-04 21:07:10.208 INFO  o.s.b.w.e.t.TomcatWebServer:243 - Tomcat started on port 8081 (http) with context path '/'
2025-06-04 21:07:10.216 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.985 seconds (process running for 1.386)
2025-06-04 21:07:15.127 INFO  o.a.c.c.C.[.[.[/]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 21:07:15.128 INFO  o.s.w.s.DispatcherServlet:532 - Initializing Servlet 'dispatcherServlet'
2025-06-04 21:07:15.129 INFO  o.s.w.s.DispatcherServlet:554 - Completed initialization in 1 ms
2025-06-04 21:07:15.140 WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver:254 - Resolved [org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' is not supported]
2025-06-04 21:07:16.086 WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver:254 - Resolved [org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' is not supported]
2025-06-04 21:07:17.630 WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver:254 - Resolved [org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' is not supported]
2025-06-04 21:07:35.537 INFO  o.s.b.w.e.t.GracefulShutdown:54 - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-04 21:07:37.880 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 21:07:37.906 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 87533 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 21:07:37.906 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-04 21:07:38.198 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=ad2b7412-5158-310d-b612-b9303e150e92
2025-06-04 21:07:38.330 INFO  o.s.b.w.e.t.TomcatWebServer:111 - Tomcat initialized with port 8081 (http)
2025-06-04 21:07:38.335 INFO  o.a.c.h.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-04 21:07:38.336 INFO  o.a.c.c.StandardService:173 - Starting service [Tomcat]
2025-06-04 21:07:38.336 INFO  o.a.c.c.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-06-04 21:07:38.358 INFO  o.a.c.c.C.[.[.[/]:173 - Initializing Spring embedded WebApplicationContext
2025-06-04 21:07:38.358 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:301 - Root WebApplicationContext: initialization completed in 438 ms
2025-06-04 21:07:38.680 INFO  o.a.c.h.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8081"]
2025-06-04 21:07:38.685 INFO  o.s.b.w.e.t.TomcatWebServer:243 - Tomcat started on port 8081 (http) with context path '/'
2025-06-04 21:07:38.693 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.007 seconds (process running for 1.367)
2025-06-04 21:07:47.726 INFO  o.a.c.c.C.[.[.[/]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 21:07:47.726 INFO  o.s.w.s.DispatcherServlet:532 - Initializing Servlet 'dispatcherServlet'
2025-06-04 21:07:47.729 INFO  o.s.w.s.DispatcherServlet:554 - Completed initialization in 3 ms
2025-06-04 21:08:21.143 INFO  o.s.b.w.e.t.GracefulShutdown:54 - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-04 21:08:21.159 INFO  o.s.b.w.e.t.GracefulShutdown:76 - Graceful shutdown complete
2025-06-04 21:08:24.146 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 21:08:24.171 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 87573 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 21:08:24.171 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-04 21:08:24.451 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=e96692bd-c369-3c4c-a69e-fbdefb8f6d3f
2025-06-04 21:08:24.566 INFO  o.s.b.w.e.t.TomcatWebServer:111 - Tomcat initialized with port 8081 (http)
2025-06-04 21:08:24.571 INFO  o.a.c.h.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8081"]
2025-06-04 21:08:24.572 INFO  o.a.c.c.StandardService:173 - Starting service [Tomcat]
2025-06-04 21:08:24.572 INFO  o.a.c.c.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-06-04 21:08:24.591 INFO  o.a.c.c.C.[.[.[/]:173 - Initializing Spring embedded WebApplicationContext
2025-06-04 21:08:24.592 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:301 - Root WebApplicationContext: initialization completed in 405 ms
2025-06-04 21:08:24.875 INFO  o.a.c.h.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8081"]
2025-06-04 21:08:24.881 INFO  o.s.b.w.e.t.TomcatWebServer:243 - Tomcat started on port 8081 (http) with context path '/'
2025-06-04 21:08:24.888 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.932 seconds (process running for 1.285)
2025-06-04 21:08:31.930 INFO  o.a.c.c.C.[.[.[/]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 21:08:31.930 INFO  o.s.w.s.DispatcherServlet:532 - Initializing Servlet 'dispatcherServlet'
2025-06-04 21:08:31.932 INFO  o.s.w.s.DispatcherServlet:554 - Completed initialization in 1 ms
2025-06-04 21:08:45.822 INFO  o.s.b.w.e.t.GracefulShutdown:54 - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-04 21:08:45.838 INFO  o.s.b.w.e.t.GracefulShutdown:76 - Graceful shutdown complete
2025-06-04 21:09:00.253 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 21:09:00.278 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 87616 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 21:09:00.278 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-04 21:09:00.539 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=30f94c34-81b9-35c6-9085-41a4b5995958
2025-06-04 21:09:00.565 WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext:635 - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start reactive web server
2025-06-04 21:09:00.569 INFO  o.s.b.a.l.ConditionEvaluationReportLogger:82 - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-04 21:09:00.577 ERROR o.s.b.d.LoggingFailureAnalysisReporter:40 - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web application could not be started as there was no org.springframework.boot.web.reactive.server.ReactiveWebServerFactory bean defined in the context.

Action:

Check your application's dependencies for a supported reactive web server.
Check the configured web application type.

2025-06-04 21:09:07.828 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 21:09:07.851 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 87632 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 21:09:07.851 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-04 21:09:08.141 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=30f94c34-81b9-35c6-9085-41a4b5995958
2025-06-04 21:09:08.164 WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext:635 - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start reactive web server
2025-06-04 21:09:08.168 INFO  o.s.b.a.l.ConditionEvaluationReportLogger:82 - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-04 21:09:08.175 ERROR o.s.b.d.LoggingFailureAnalysisReporter:40 - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web application could not be started as there was no org.springframework.boot.web.reactive.server.ReactiveWebServerFactory bean defined in the context.

Action:

Check your application's dependencies for a supported reactive web server.
Check the configured web application type.

2025-06-04 21:09:23.795 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-06-04 21:09:23.820 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 87787 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-06-04 21:09:23.821 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-06-04 21:09:24.086 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=49fcb10f-217c-3000-ae25-22ddf26da6f7
2025-06-04 21:09:24.484 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-06-04 21:09:24.491 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.88 seconds (process running for 1.34)
2025-06-04 21:09:31.173 INFO  i.m.s.McpAsyncServer:461 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=RootCapabilities[listChanged=false], sampling=null], Info: Implementation[name=cursor-vscode, version=1.0.0]
2025-06-04 21:09:31.176 WARN  i.m.s.McpAsyncServer:477 - Client requested unsupported protocol version: 2025-03-26, so the server will sugggest the 2024-11-05 version instead
