2025-07-23 11:01:01.455 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-23 11:01:01.489 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 32655 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by z<PERSON>han25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-23 11:01:01.490 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-07-23 11:01:01.826 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=385a9520-b5eb-3ecd-9ce1-193c3ec6ce65
2025-07-23 11:01:02.279 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-23 11:01:02.291 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.113 seconds (process running for 1.615)
2025-07-23 11:03:43.916 INFO  i.m.s.McpAsyncServer:197 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=JoyCode, version=3.1.2]
2025-07-23 11:03:43.919 WARN  i.m.s.McpAsyncServer:213 - Client requested unsupported protocol version: 2025-03-26, so the server will suggest the 2024-11-05 version instead
2025-07-23 11:12:30.357 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-23 11:12:38.241 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-23 11:12:38.277 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 34039 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-23 11:12:38.278 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-07-23 11:12:38.647 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=385a9520-b5eb-3ecd-9ce1-193c3ec6ce65
2025-07-23 11:12:39.074 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-23 11:12:39.085 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.129 seconds (process running for 2.201)
2025-07-23 11:13:00.473 INFO  i.m.s.McpAsyncServer:197 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=JoyCode, version=3.1.2]
2025-07-23 11:13:00.482 WARN  i.m.s.McpAsyncServer:213 - Client requested unsupported protocol version: 2025-03-26, so the server will suggest the 2024-11-05 version instead
2025-07-23 11:15:22.182 INFO  i.m.s.McpAsyncServer:197 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=JoyCode, version=3.1.2]
2025-07-23 11:15:22.184 WARN  i.m.s.McpAsyncServer:213 - Client requested unsupported protocol version: 2025-03-26, so the server will suggest the 2024-11-05 version instead
2025-07-23 11:27:28.392 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-23 11:33:10.987 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-07-23 11:33:11.013 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 38560 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-23 11:33:11.013 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-07-23 11:33:11.266 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=b34571f8-871b-3430-82ca-6dd44132418c
2025-07-23 11:33:11.632 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-23 11:33:11.643 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.916 seconds (process running for 1.076)
2025-07-23 11:33:57.965 INFO  c.j.j.j.a.m.c.StreamController:69 - 接收到同步写作请求 - sessionId: test-session-001, userName: testuser, intention: 帮我写一份关于人工智能技术发展的简短报告
2025-07-23 11:33:57.972 INFO  c.j.j.j.a.m.s.JoyEditTollService:154 - 开始处理同步写作请求 - sessionId: test-session-001, userName: testuser
2025-07-23 11:33:58.193 ERROR c.j.j.j.a.m.s.JoyEditTollService:168 - 同步写作请求失败 - sessionId: test-session-001, error: [302] during [POST] to [https://joyedit-dev.jd.com/api/v1/llm/chat/doron] [JoyEditService#chatDoronStream(String,DoronChatDTO)]: []
feign.FeignException: [302] during [POST] to [https://joyedit-dev.jd.com/api/v1/llm/chat/doron] [JoyEditService#chatDoronStream(String,DoronChatDTO)]: []
	at feign.FeignException.errorStatus(FeignException.java:208)
	at feign.FeignException.errorStatus(FeignException.java:194)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:103)
	at feign.InvocationContext.decodeError(InvocationContext.java:126)
	at feign.InvocationContext.proceed(InvocationContext.java:72)
	at feign.ResponseHandler.handleResponse(ResponseHandler.java:63)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:114)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99)
	at jdk.proxy2/jdk.proxy2.$Proxy68.chatDoronStream(Unknown Source)
	at com.jd.jdt.joylaw.ai.mcp.service.JoyEditTollService.chatDoronJoyEditSync(JoyEditTollService.java:162)
	at com.jd.jdt.joylaw.ai.mcp.controller.StreamController.syncChatEdit(StreamController.java:72)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onNext(FluxDefaultIfEmpty.java:122)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:193)
	at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
	at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onSubscribe(FluxMapFuseable.java:96)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onSubscribe(MonoPeekTerminal.java:152)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:207)
	at reactor.core.publisher.FluxIterable$IterableSubscription.slowPath(FluxIterable.java:335)
	at reactor.core.publisher.FluxIterable$IterableSubscription.request(FluxIterable.java:294)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerComplete(FluxConcatMapNoPrefetch.java:275)
	at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onComplete(FluxConcatMap.java:889)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.MonoNext$NextSubscriber.onComplete(MonoNext.java:102)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onComplete(FluxConcatMapNoPrefetch.java:241)
	at reactor.core.publisher.FluxIterable$IterableSubscription.slowPath(FluxIterable.java:357)
	at reactor.core.publisher.FluxIterable$IterableSubscription.request(FluxIterable.java:294)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
	at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.request(FluxPeek.java:138)
	at reactor.core.publisher.FluxMap$MapSubscriber.request(FluxMap.java:164)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
	at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
	at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
	at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
	at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
	at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-23 11:38:26.536 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-23 11:38:26.563 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 39226 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-23 11:38:26.563 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-23 11:38:26.882 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=b34571f8-871b-3430-82ca-6dd44132418c
2025-07-23 11:38:29.419 WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext:635 - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-07-23 11:38:29.437 INFO  o.s.b.a.l.ConditionEvaluationReportLogger:82 - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-23 11:38:29.448 ERROR o.s.b.d.LoggingFailureAnalysisReporter:40 - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-07-23 11:45:02.493 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-07-23 11:45:02.528 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 40515 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-23 11:45:02.528 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-23 11:45:02.780 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=882b9e15-59cc-3e12-948e-784a25f2863b
2025-07-23 11:45:05.302 WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext:635 - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-07-23 11:45:05.322 INFO  o.s.b.a.l.ConditionEvaluationReportLogger:82 - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-23 11:45:05.334 ERROR o.s.b.d.LoggingFailureAnalysisReporter:40 - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-07-23 11:46:09.956 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-23 11:46:51.030 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-07-23 11:46:51.054 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 41205 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-23 11:46:51.055 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-07-23 11:46:51.305 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=882b9e15-59cc-3e12-948e-784a25f2863b
2025-07-23 11:46:51.735 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-23 11:46:51.748 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.899 seconds (process running for 1.025)
2025-07-23 11:47:59.021 INFO  c.j.j.j.a.m.c.StreamController:69 - 接收到同步写作请求 - sessionId: test-session-001, userName: testuser, intention: 帮我写一份关于人工智能技术发展的简短报告
2025-07-23 11:47:59.022 INFO  c.j.j.j.a.m.s.JoyEditTollService:246 - 开始处理同步写作请求 - sessionId: test-session-001, userName: testuser
2025-07-23 11:47:59.383 INFO  c.j.j.j.a.m.s.JoyEditTollService:256 - 同步写作请求完成 - sessionId: test-session-001
2025-07-23 11:48:29.907 INFO  c.j.j.j.a.m.c.StreamController:69 - 接收到同步写作请求 - sessionId: test-session-001, userName: testuser, intention: 帮我写一份关于人工智能技术发展的简短报告
2025-07-23 11:48:29.908 INFO  c.j.j.j.a.m.s.JoyEditTollService:246 - 开始处理同步写作请求 - sessionId: test-session-001, userName: testuser
2025-07-23 11:48:30.058 INFO  c.j.j.j.a.m.s.JoyEditTollService:256 - 同步写作请求完成 - sessionId: test-session-001
2025-07-23 11:58:56.631 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-23 11:59:39.513 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-07-23 11:59:39.538 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 43879 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-23 11:59:39.538 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-07-23 11:59:39.825 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=3bc6b91f-9c7b-3081-bdf8-30a02bdbc157
2025-07-23 11:59:40.317 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-23 11:59:40.330 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.003 seconds (process running for 1.136)
2025-07-23 12:00:14.233 INFO  i.m.s.McpAsyncServer:197 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=JoyCode, version=3.1.2]
2025-07-23 12:00:14.236 WARN  i.m.s.McpAsyncServer:213 - Client requested unsupported protocol version: 2025-03-26, so the server will suggest the 2024-11-05 version instead
2025-07-23 12:03:05.294 INFO  c.j.j.j.a.m.s.JoyEditTollService:239 - 开始处理同步写作请求 - sessionId: 123123123123, userName: 赵晗25
2025-07-23 12:03:05.316 ERROR c.j.j.j.a.m.s.JoyEditTollService:253 - 同步写作请求失败 - sessionId: 123123123123, error: Unexpected char 0x8d75 at 0 in user_name value: 赵晗25
java.lang.IllegalArgumentException: Unexpected char 0x8d75 at 0 in user_name value: 赵晗25
	at okhttp3.Headers$Companion.checkValue(Headers.kt:450)
	at okhttp3.Headers$Companion.access$checkValue(Headers.kt:362)
	at okhttp3.Headers$Builder.add(Headers.kt:261)
	at okhttp3.Request$Builder.addHeader(Request.kt:210)
	at feign.okhttp.OkHttpClient.toOkHttpRequest(OkHttpClient.java:65)
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:176)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99)
	at jdk.proxy2/jdk.proxy2.$Proxy74.chatDoronStream(Unknown Source)
	at com.jd.jdt.joylaw.ai.mcp.service.JoyEditTollService.chatDoronJoyEditSync(JoyEditTollService.java:247)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.ai.tool.method.MethodToolCallback.callMethod(MethodToolCallback.java:158)
	at org.springframework.ai.tool.method.MethodToolCallback.call(MethodToolCallback.java:108)
	at org.springframework.ai.mcp.McpToolUtils.lambda$toSyncToolSpecification$0(McpToolUtils.java:175)
	at org.springframework.ai.mcp.McpToolUtils.lambda$toAsyncToolSpecification$1(McpToolUtils.java:292)
	at reactor.core.publisher.MonoCallable.call(MonoCallable.java:72)
	at reactor.core.publisher.FluxSubscribeOnCallable$CallableSubscribeOnSubscription.run(FluxSubscribeOnCallable.java:228)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-23 12:03:16.080 INFO  c.j.j.j.a.m.s.JoyEditTollService:239 - 开始处理同步写作请求 - sessionId: 123123123123, userName: zhaohan25
2025-07-23 12:03:16.406 INFO  c.j.j.j.a.m.s.JoyEditTollService:249 - 同步写作请求完成 - sessionId: 123123123123
2025-07-23 12:04:24.079 INFO  c.j.j.j.a.m.s.JoyEditTollService:54 - 开始处理MCP流式写作请求 - sessionId: 123123123123, userName: zhaohan25
2025-07-23 12:05:02.657 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-07-23 12:05:02.680 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 44769 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-23 12:05:02.680 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-23 12:05:02.920 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=3bc6b91f-9c7b-3081-bdf8-30a02bdbc157
2025-07-23 12:05:05.389 WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext:635 - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-07-23 12:05:05.406 INFO  o.s.b.a.l.ConditionEvaluationReportLogger:82 - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-23 12:05:05.420 ERROR o.s.b.d.LoggingFailureAnalysisReporter:40 - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-07-23 12:13:02.489 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-07-23 12:13:02.515 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 46570 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-23 12:13:02.515 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-07-23 12:13:02.767 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=3bc6b91f-9c7b-3081-bdf8-30a02bdbc157
2025-07-23 12:13:05.367 WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext:635 - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-07-23 12:13:05.376 INFO  o.s.b.a.l.ConditionEvaluationReportLogger:82 - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-23 12:13:05.390 ERROR o.s.b.d.LoggingFailureAnalysisReporter:40 - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-07-23 12:13:57.415 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-07-23 12:13:57.438 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 47008 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-23 12:13:57.439 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-07-23 12:13:57.673 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=3bc6b91f-9c7b-3081-bdf8-30a02bdbc157
2025-07-23 12:13:58.082 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-23 12:13:58.094 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.851 seconds (process running for 0.968)
2025-07-23 12:14:19.384 INFO  i.m.s.McpAsyncServer:197 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=JoyCode, version=3.1.2]
2025-07-23 12:14:19.387 WARN  i.m.s.McpAsyncServer:213 - Client requested unsupported protocol version: 2025-03-26, so the server will suggest the 2024-11-05 version instead
2025-07-23 12:15:00.881 INFO  i.m.s.McpAsyncServer:197 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=JoyCode, version=3.1.2]
2025-07-23 12:15:00.882 WARN  i.m.s.McpAsyncServer:213 - Client requested unsupported protocol version: 2025-03-26, so the server will suggest the 2024-11-05 version instead
2025-07-23 13:02:38.526 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-23 13:03:08.536 INFO  o.s.c.s.DefaultLifecycleProcessor:536 - Shutdown phase 2147482623 ends with 1 bean still running after timeout of 30000ms: [webServerGracefulShutdown]
2025-07-23 13:03:08.537 INFO  o.s.b.w.e.n.GracefulShutdown:66 - Graceful shutdown aborted with one or more requests still active
2025-07-23 13:03:58.786 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-23 13:03:58.819 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 52782 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-23 13:03:58.820 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-07-23 13:03:59.191 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=30225ff4-a79e-3a77-9ddf-1fd61959a43e
2025-07-23 13:03:59.734 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-23 13:03:59.746 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.286 seconds (process running for 2.303)
2025-07-23 13:04:36.581 INFO  i.m.s.McpAsyncServer:197 - Client initialize request - Protocol: 2025-03-26, Capabilities: ClientCapabilities[experimental=null, roots=null, sampling=null], Info: Implementation[name=JoyCode, version=3.1.2]
2025-07-23 13:04:36.585 WARN  i.m.s.McpAsyncServer:213 - Client requested unsupported protocol version: 2025-03-26, so the server will suggest the 2024-11-05 version instead
2025-07-23 13:05:58.447 INFO  c.j.j.j.a.m.s.JoyEditTollService:284 - 开始MCP流式测试 - sessionId: test-1753247158442, testMessage: 这是一个流式返回测试, chunks: 10, delay: 500ms
2025-07-23 13:06:10.509 INFO  c.j.j.j.a.m.s.JoyEditTollService:357 - 开始简单MCP流式测试 - sessionId: simple-test-1753247170509, message: 这是一个简单的流式返回测试
2025-07-23 13:08:22.134 INFO  c.j.j.j.a.m.s.JoyEditTollService:239 - 开始处理同步写作请求 - sessionId: 123123123123, userName: 赵晗25
2025-07-23 13:08:22.196 ERROR c.j.j.j.a.m.s.JoyEditTollService:253 - 同步写作请求失败 - sessionId: 123123123123, error: Unexpected char 0x8d75 at 0 in user_name value: 赵晗25
java.lang.IllegalArgumentException: Unexpected char 0x8d75 at 0 in user_name value: 赵晗25
	at okhttp3.Headers$Companion.checkValue(Headers.kt:450)
	at okhttp3.Headers$Companion.access$checkValue(Headers.kt:362)
	at okhttp3.Headers$Builder.add(Headers.kt:261)
	at okhttp3.Request$Builder.addHeader(Request.kt:210)
	at feign.okhttp.OkHttpClient.toOkHttpRequest(OkHttpClient.java:65)
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:176)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99)
	at jdk.proxy2/jdk.proxy2.$Proxy75.chatDoronStream(Unknown Source)
	at com.jd.jdt.joylaw.ai.mcp.service.JoyEditTollService.chatDoronJoyEditSync(JoyEditTollService.java:247)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.ai.tool.method.MethodToolCallback.callMethod(MethodToolCallback.java:158)
	at org.springframework.ai.tool.method.MethodToolCallback.call(MethodToolCallback.java:108)
	at org.springframework.ai.mcp.McpToolUtils.lambda$toSyncToolSpecification$0(McpToolUtils.java:175)
	at org.springframework.ai.mcp.McpToolUtils.lambda$toAsyncToolSpecification$1(McpToolUtils.java:292)
	at reactor.core.publisher.MonoCallable.call(MonoCallable.java:72)
	at reactor.core.publisher.MonoCallableOnAssembly.call(MonoCallableOnAssembly.java:91)
	at reactor.core.publisher.FluxSubscribeOnCallable$CallableSubscribeOnSubscription.run(FluxSubscribeOnCallable.java:228)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68)
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-23 13:08:32.535 INFO  c.j.j.j.a.m.s.JoyEditTollService:239 - 开始处理同步写作请求 - sessionId: 123123123123, userName: zhaohan25
2025-07-23 13:08:32.897 INFO  c.j.j.j.a.m.s.JoyEditTollService:249 - 同步写作请求完成 - sessionId: 123123123123
2025-07-23 13:08:44.847 INFO  c.j.j.j.a.m.s.JoyEditTollService:357 - 开始简单MCP流式测试 - sessionId: simple-test-1753247324847, message: 测试MCP流式功能
2025-07-23 13:14:05.036 INFO  c.j.j.j.a.m.c.StreamController:42 - 收到MCP初始化请求: {"jsonrpc":"2.0","id":"test-1","method":"initialize","params":{"protocol":"2024-11-05"}}
2025-07-23 13:14:05.040 INFO  c.j.j.j.a.m.c.StreamController:52 - 创建MCP会话: eaea8ec1-b19b-4b56-a662-c371fd23c689
2025-07-23 13:15:10.916 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-23 13:15:14.903 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-23 13:15:14.933 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 54793 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-23 13:15:14.934 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "dev"
2025-07-23 13:15:15.277 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=30225ff4-a79e-3a77-9ddf-1fd61959a43e
2025-07-23 13:15:15.807 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-23 13:15:15.817 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 1.169 seconds (process running for 2.435)
2025-07-23 13:20:45.708 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-07-23 13:20:45.733 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 55568 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-23 13:20:45.734 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-23 13:20:46.026 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=30225ff4-a79e-3a77-9ddf-1fd61959a43e
2025-07-23 13:20:48.542 WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext:635 - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-07-23 13:20:48.551 INFO  o.s.b.a.l.ConditionEvaluationReportLogger:82 - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-23 13:20:48.560 ERROR o.s.b.d.LoggingFailureAnalysisReporter:40 - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-07-23 13:20:52.842 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-23 13:21:19.830 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-07-23 13:21:19.870 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 55754 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-23 13:21:19.871 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-23 13:21:20.109 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=30225ff4-a79e-3a77-9ddf-1fd61959a43e
2025-07-23 13:21:20.531 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-23 13:21:20.544 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.926 seconds (process running for 1.059)
2025-07-23 13:24:32.962 ERROR i.m.s.McpServerSession:251 - No handler registered for notification method: notifications/cancelled
2025-07-23 13:24:50.204 INFO  c.j.j.j.a.m.c.StreamController:42 - 收到MCP初始化请求: {"jsonrpc":"2.0","id":"3f1f4834-3721-41dc-9bb9-9899da32c42f","method":"initialize","params":{"protocol":"2024-11-05"}}
2025-07-23 13:24:50.205 INFO  c.j.j.j.a.m.c.StreamController:52 - 创建MCP会话: 45e123ef-5c2b-425e-937e-47c18d199c6b
2025-07-23 13:25:12.747 INFO  c.j.j.j.a.m.c.StreamController:42 - 收到MCP初始化请求: {
    "jsonrpc": "2.0",
    "id": "test-init-001",
    "method": "initialize",
    "params": {
      "protocol": "2024-11-05"
    }
  }
2025-07-23 13:25:12.747 INFO  c.j.j.j.a.m.c.StreamController:52 - 创建MCP会话: e11e86c3-bcf3-46bc-989e-e2ab2228dc76
2025-07-23 13:26:38.703 INFO  o.s.b.w.e.n.GracefulShutdown:53 - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-23 13:26:54.881 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

[32m :: Spring Boot :: [39m              [2m (v3.4.4)[0;39m


2025-07-23 13:26:54.906 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 58420 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-23 13:26:54.906 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-23 13:26:55.150 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=94573a00-ab2b-38fb-97d8-d2e333a8872a
2025-07-23 13:26:55.574 INFO  o.s.b.w.e.n.NettyWebServer:126 - Netty started on port 8081 (http)
2025-07-23 13:26:55.586 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:59 - Started JoyLawMcpServerApplication in 0.94 seconds (process running for 1.069)
2025-07-23 13:27:08.724 INFO  o.s.c.s.DefaultLifecycleProcessor:536 - Shutdown phase 2147482623 ends with 1 bean still running after timeout of 30000ms: [webServerGracefulShutdown]
2025-07-23 13:27:08.728 INFO  o.s.b.w.e.n.GracefulShutdown:66 - Graceful shutdown aborted with one or more requests still active
2025-07-23 13:27:14.111 INFO  c.j.j.j.a.m.c.StreamTestController:31 - 开始测试简单MCP流式工具 - message: 这是第一个MCP流式测试消息！
2025-07-23 13:27:14.112 INFO  c.j.j.j.a.m.s.JoyEditTollService:357 - 开始简单MCP流式测试 - sessionId: simple-test-1753248434112, message: 这是第一个MCP流式测试消息！
2025-07-23 13:27:14.114 INFO  c.j.j.j.a.m.s.JoyEditTollService:375 - 创建简单MCP流式测试 - sessionId: simple-test-1753248434112
2025-07-23 13:27:14.115 INFO  c.j.j.j.a.m.s.JoyEditTollService:376 - 简单MCP流式测试创建成功 - sessionId: simple-test-1753248434112
2025-07-23 13:27:14.116 INFO  c.j.j.j.a.m.s.JoyEditTollService:370 - 简单MCP流式测试开始 - sessionId: simple-test-1753248434112
2025-07-23 13:27:14.922 INFO  c.j.j.j.a.m.c.StreamTestController:35 - 发送流式数据块: 🔄 开始MCP流式测试...


2025-07-23 13:27:15.729 INFO  c.j.j.j.a.m.c.StreamTestController:35 - 发送流式数据块: 📝 您的消息: 这是第一个MCP流式测试消息！


2025-07-23 13:27:16.536 INFO  c.j.j.j.a.m.c.StreamTestController:35 - 发送流式数据块: ⚡ 正在通过MCP协议传输数据...


2025-07-23 13:27:17.343 INFO  c.j.j.j.a.m.c.StreamTestController:35 - 发送流式数据块: ✅ MCP流式传输正常工作！


2025-07-23 13:27:18.149 INFO  c.j.j.j.a.m.c.StreamTestController:35 - 发送流式数据块: 🎯 测试完成 - 会话ID: simple-test-1753248434112

2025-07-23 13:27:18.151 INFO  c.j.j.j.a.m.s.JoyEditTollService:372 - 简单MCP流式测试完成 - sessionId: simple-test-1753248434112
2025-07-23 13:27:18.152 INFO  c.j.j.j.a.m.c.StreamTestController:36 - 简单MCP流式测试完成
2025-07-23 13:27:34.347 INFO  c.j.j.j.a.m.c.StreamTestController:49 - 开始测试完整MCP流式工具 - testMessage: 这是完整的MCP流式测试！, chunkCount: 8, delayMs: 300
2025-07-23 13:27:34.349 INFO  c.j.j.j.a.m.s.JoyEditTollService:284 - 开始MCP流式测试 - sessionId: test-1753248454348, testMessage: 这是完整的MCP流式测试！, chunks: 8, delay: 300ms
2025-07-23 13:27:34.351 INFO  c.j.j.j.a.m.s.JoyEditTollService:288 - MCP流式测试开始 - sessionId: test-1753248454348
2025-07-23 13:27:34.351 INFO  c.j.j.j.a.m.s.JoyEditTollService:297 - 创建MCP测试流式数据 - sessionId: test-1753248454348, chunks: 8, delay: 300ms
2025-07-23 13:27:34.352 INFO  c.j.j.j.a.m.s.JoyEditTollService:289 - MCP流式测试创建成功 - sessionId: test-1753248454348
2025-07-23 13:27:34.353 INFO  c.j.j.j.a.m.s.JoyEditTollService:322 - MCP测试流式数据开始发送 - sessionId: test-1753248454348
2025-07-23 13:27:34.659 INFO  c.j.j.j.a.m.c.StreamTestController:54 - 发送流式数据块: 🚀 MCP流式测试开始
测试消息: 这是完整的MCP流式测试！
总共将发送 8 个数据块


2025-07-23 13:27:34.964 INFO  c.j.j.j.a.m.c.StreamTestController:54 - 发送流式数据块: 📦 第 2/8 个数据块
- 时间戳: 2025-07-23T13:27:34.662143
- 会话ID: test-1753248454348
- 状态: 正在传输...


2025-07-23 13:27:35.270 INFO  c.j.j.j.a.m.c.StreamTestController:54 - 发送流式数据块: 📦 第 3/8 个数据块
- 时间戳: 2025-07-23T13:27:34.964937
- 会话ID: test-1753248454348
- 状态: 正在传输...


2025-07-23 13:27:35.577 INFO  c.j.j.j.a.m.c.StreamTestController:54 - 发送流式数据块: 📦 第 4/8 个数据块
- 时间戳: 2025-07-23T13:27:35.272288
- 会话ID: test-1753248454348
- 状态: 正在传输...


2025-07-23 13:27:35.884 INFO  c.j.j.j.a.m.c.StreamTestController:54 - 发送流式数据块: 📦 第 5/8 个数据块
- 时间戳: 2025-07-23T13:27:35.578509
- 会话ID: test-1753248454348
- 状态: 正在传输...


2025-07-23 13:27:36.190 INFO  c.j.j.j.a.m.c.StreamTestController:54 - 发送流式数据块: 📦 第 6/8 个数据块
- 时间戳: 2025-07-23T13:27:35.885305
- 会话ID: test-1753248454348
- 状态: 正在传输...


2025-07-23 13:27:36.493 INFO  c.j.j.j.a.m.c.StreamTestController:54 - 发送流式数据块: 📦 第 7/8 个数据块
- 时间戳: 2025-07-23T13:27:36.192411
- 会话ID: test-1753248454348
- 状态: 正在传输...


2025-07-23 13:27:36.800 INFO  c.j.j.j.a.m.c.StreamTestController:54 - 发送流式数据块: ✅ 第 8/8 个数据块 - MCP流式测试完成！

测试结果:
- MCP协议流式传输: ✅ 正常
- 数据块传输: ✅ 完整
- 延迟控制: ✅ 正常
- 会话ID: test-1753248454348

🎉 MCP流式功能验证成功！
2025-07-23 13:27:36.802 INFO  c.j.j.j.a.m.s.JoyEditTollService:324 - MCP测试流式数据发送完成 - sessionId: test-1753248454348
2025-07-23 13:27:36.803 INFO  c.j.j.j.a.m.c.StreamTestController:55 - 完整MCP流式测试完成
2025-07-23 13:27:55.401 INFO  c.j.j.j.a.m.c.StreamTestController:68 - 开始测试写作意图MCP流式工具 - sessionId: test-writing-session-001, userName: testUser, intentionDesc: 请帮我写一份技术文档，介绍MCP流式协议的实现原理和测试方法
2025-07-23 13:27:55.402 INFO  c.j.j.j.a.m.s.JoyEditTollService:54 - 开始处理MCP流式写作请求 - sessionId: test-writing-session-001, userName: testUser
2025-07-23 13:27:55.411 INFO  c.j.j.j.a.m.s.JoyEditTollService:104 - MCP流式请求开始 - sessionId: test-writing-session-001
2025-07-23 13:27:55.411 INFO  c.j.j.j.a.m.s.JoyEditTollService:61 - 准备调用远程流式接口 - sessionId: test-writing-session-001
2025-07-23 13:27:55.508 INFO  c.j.j.j.a.m.s.JoyEditTollService:62 - 请求参数 - userName: testUser, doronChatDTO: {"messages":[{"chatRole":"USER","level":0,"messageDetailList":[{"chatContent":"请帮我写一份技术文档，介绍MCP流式协议的实现原理和测试方法","msgType":"TEXT"}]}],"sessionId":"test-writing-session-001","stream":true}
2025-07-23 13:27:55.904 INFO  c.j.j.j.a.m.s.JoyEditTollService:72 - 远程服务响应状态 - status: 200, headers: {cache-control=[max-age=0], connection=[close], content-language=[zh-CN], content-type=[text/html;charset=UTF-8], date=[Wed, 23 Jul 2025 05:27:55 GMT], expires=[Wed, 23 Jul 2025 05:27:55 GMT], server=[openresty], set-cookie=[ssa.trace.res=40c26c05ef0045fc87f3a09c8a26930c; Domain=ssa.jd.com; Path=/; Secure; HttpOnly], transfer-encoding=[chunked], vary=[Accept-Encoding]}
2025-07-23 13:27:55.909 INFO  c.j.j.j.a.m.s.JoyEditTollService:105 - MCP流式请求成功创建 - sessionId: test-writing-session-001
2025-07-23 13:27:55.913 INFO  c.j.j.j.a.m.s.JoyEditTollService:148 - MCP流式数据开始接收 - sessionId: test-writing-session-001
2025-07-23 13:27:55.917 INFO  c.j.j.j.a.m.s.JoyEditTollService:153 - MCP流式数据接收完成 - sessionId: test-writing-session-001
2025-07-23 13:27:55.917 INFO  c.j.j.j.a.m.c.StreamTestController:74 - 写作意图MCP流式测试完成
2025-07-23 13:32:13.105 ERROR i.m.s.McpServerSession:251 - No handler registered for notification method: notifications/cancelled
2025-07-23 13:58:07.024 INFO  o.s.b.SpringApplication:58 - 
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.4)


2025-07-23 13:58:07.055 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:53 - Starting JoyLawMcpServerApplication using Java 17.0.15 with PID 62709 (/Users/<USER>/IdeaProjects/mcp-server/target/classes started by zhaohan25 in /Users/<USER>/IdeaProjects/mcp-server)
2025-07-23 13:58:07.056 INFO  c.j.j.j.a.m.JoyLawMcpServerApplication:658 - The following 1 profile is active: "local"
2025-07-23 13:58:07.406 INFO  o.s.c.c.s.GenericScope:282 - BeanFactory id=94573a00-ab2b-38fb-97d8-d2e333a8872a
2025-07-23 13:58:10.029 WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext:635 - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-07-23 13:58:10.044 INFO  o.s.b.a.l.ConditionEvaluationReportLogger:82 - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-23 13:58:10.054 ERROR o.s.b.d.LoggingFailureAnalysisReporter:40 - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-07-23 14:02:10.112 INFO  c.j.j.j.a.m.c.StreamController:42 - 收到MCP初始化请求: {"jsonrpc":"2.0","id":"23773769-2cac-42d7-b038-fcb800ce1064","method":"initialize","params":{"protocol":"2024-11-05"}}
2025-07-23 14:02:10.114 INFO  c.j.j.j.a.m.c.StreamController:52 - 创建MCP会话: 1ceb292d-a773-4258-87f6-8f840c83a635
2025-07-23 14:02:51.656 INFO  c.j.j.j.a.m.c.StreamTestController:31 - 开始测试简单MCP流式工具 - message: 这是一个流式输出测试，用于验证MCP协议的流式传输功能
2025-07-23 14:02:51.658 INFO  c.j.j.j.a.m.s.JoyEditTollService:357 - 开始简单MCP流式测试 - sessionId: simple-test-1753250571658, message: 这是一个流式输出测试，用于验证MCP协议的流式传输功能
2025-07-23 14:02:51.659 INFO  c.j.j.j.a.m.s.JoyEditTollService:375 - 创建简单MCP流式测试 - sessionId: simple-test-1753250571658
2025-07-23 14:02:51.659 INFO  c.j.j.j.a.m.s.JoyEditTollService:376 - 简单MCP流式测试创建成功 - sessionId: simple-test-1753250571658
2025-07-23 14:02:51.659 INFO  c.j.j.j.a.m.s.JoyEditTollService:370 - 简单MCP流式测试开始 - sessionId: simple-test-1753250571658
2025-07-23 14:02:52.465 INFO  c.j.j.j.a.m.c.StreamTestController:35 - 发送流式数据块: 🔄 开始MCP流式测试...


2025-07-23 14:02:53.268 INFO  c.j.j.j.a.m.c.StreamTestController:35 - 发送流式数据块: 📝 您的消息: 这是一个流式输出测试，用于验证MCP协议的流式传输功能


2025-07-23 14:02:54.074 INFO  c.j.j.j.a.m.c.StreamTestController:35 - 发送流式数据块: ⚡ 正在通过MCP协议传输数据...


2025-07-23 14:02:54.878 INFO  c.j.j.j.a.m.c.StreamTestController:35 - 发送流式数据块: ✅ MCP流式传输正常工作！


2025-07-23 14:02:55.682 INFO  c.j.j.j.a.m.c.StreamTestController:35 - 发送流式数据块: 🎯 测试完成 - 会话ID: simple-test-1753250571658

2025-07-23 14:02:55.684 INFO  c.j.j.j.a.m.s.JoyEditTollService:372 - 简单MCP流式测试完成 - sessionId: simple-test-1753250571658
2025-07-23 14:02:55.684 INFO  c.j.j.j.a.m.c.StreamTestController:36 - 简单MCP流式测试完成
2025-07-23 14:03:08.491 INFO  c.j.j.j.a.m.c.StreamTestController:49 - 开始测试完整MCP流式工具 - testMessage: 完整流式测试, chunkCount: 8, delayMs: 800
2025-07-23 14:03:08.491 INFO  c.j.j.j.a.m.s.JoyEditTollService:284 - 开始MCP流式测试 - sessionId: test-1753250588491, testMessage: 完整流式测试, chunks: 8, delay: 800ms
2025-07-23 14:03:08.492 INFO  c.j.j.j.a.m.s.JoyEditTollService:288 - MCP流式测试开始 - sessionId: test-1753250588491
2025-07-23 14:03:08.492 INFO  c.j.j.j.a.m.s.JoyEditTollService:297 - 创建MCP测试流式数据 - sessionId: test-1753250588491, chunks: 8, delay: 800ms
2025-07-23 14:03:08.493 INFO  c.j.j.j.a.m.s.JoyEditTollService:289 - MCP流式测试创建成功 - sessionId: test-1753250588491
2025-07-23 14:03:08.493 INFO  c.j.j.j.a.m.s.JoyEditTollService:322 - MCP测试流式数据开始发送 - sessionId: test-1753250588491
2025-07-23 14:03:09.300 INFO  c.j.j.j.a.m.c.StreamTestController:54 - 发送流式数据块: 🚀 MCP流式测试开始
测试消息: 完整流式测试
总共将发送 8 个数据块


2025-07-23 14:03:10.105 INFO  c.j.j.j.a.m.c.StreamTestController:54 - 发送流式数据块: 📦 第 2/8 个数据块
- 时间戳: 2025-07-23T14:03:09.302236
- 会话ID: test-1753250588491
- 状态: 正在传输...


2025-07-23 14:03:10.909 INFO  c.j.j.j.a.m.c.StreamTestController:54 - 发送流式数据块: 📦 第 3/8 个数据块
- 时间戳: 2025-07-23T14:03:10.107512
- 会话ID: test-1753250588491
- 状态: 正在传输...


2025-07-23 14:03:11.716 INFO  c.j.j.j.a.m.c.StreamTestController:54 - 发送流式数据块: 📦 第 4/8 个数据块
- 时间戳: 2025-07-23T14:03:10.910890
- 会话ID: test-1753250588491
- 状态: 正在传输...


2025-07-23 14:03:12.520 INFO  c.j.j.j.a.m.c.StreamTestController:54 - 发送流式数据块: 📦 第 5/8 个数据块
- 时间戳: 2025-07-23T14:03:11.718517
- 会话ID: test-1753250588491
- 状态: 正在传输...


2025-07-23 14:03:13.326 INFO  c.j.j.j.a.m.c.StreamTestController:54 - 发送流式数据块: 📦 第 6/8 个数据块
- 时间戳: 2025-07-23T14:03:12.522703
- 会话ID: test-1753250588491
- 状态: 正在传输...


2025-07-23 14:03:14.129 INFO  c.j.j.j.a.m.c.StreamTestController:54 - 发送流式数据块: 📦 第 7/8 个数据块
- 时间戳: 2025-07-23T14:03:13.327458
- 会话ID: test-1753250588491
- 状态: 正在传输...


2025-07-23 14:03:14.936 INFO  c.j.j.j.a.m.c.StreamTestController:54 - 发送流式数据块: ✅ 第 8/8 个数据块 - MCP流式测试完成！

测试结果:
- MCP协议流式传输: ✅ 正常
- 数据块传输: ✅ 完整
- 延迟控制: ✅ 正常
- 会话ID: test-1753250588491

🎉 MCP流式功能验证成功！
2025-07-23 14:03:14.938 INFO  c.j.j.j.a.m.s.JoyEditTollService:324 - MCP测试流式数据发送完成 - sessionId: test-1753250588491
2025-07-23 14:03:14.939 INFO  c.j.j.j.a.m.c.StreamTestController:55 - 完整MCP流式测试完成
