package com.jd.jdt.joylaw.ai.mcp;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

/**
 * MCP流式测试客户端 - 符合MCP协议规范
 */
public class McpStreamingTestClient {

    private static final String BASE_URL = "http://localhost:8081";
    private static final String INITIALIZE_ENDPOINT = "/mcp/initialize";
    private static final String MESSAGE_ENDPOINT = "/mcp/message";
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    public static void main(String[] args) {
        System.out.println("开始测试MCP流式功能...");
        
        try {
            // 第1步：初始化MCP会话
            String sessionId = initializeMcpSession();
            if (sessionId == null) {
                System.err.println("初始化MCP会话失败，无法继续测试");
                return;
            }
            
            System.out.println("MCP会话初始化成功，会话ID: " + sessionId);
            
            // 第2步：测试简单流式工具
            testSimpleMcpStreamTool(sessionId);
            
            // 等待一段时间
            Thread.sleep(3000);
            
            // 第3步：测试完整流式工具
            testFullMcpStreamTool(sessionId);
            
        } catch (Exception e) {
            System.err.println("测试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 初始化MCP会话
     * @return 会话ID
     */
    private static String initializeMcpSession() {
        try {
            URL url = new URL(BASE_URL + INITIALIZE_ENDPOINT);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "application/json");
            connection.setDoOutput(true);
            
            // 创建初始化请求
            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("id", UUID.randomUUID().toString());
            requestBody.put("method", "initialize");
            
            ObjectNode params = objectMapper.createObjectNode();
            params.put("protocol", "2024-11-05");
            requestBody.set("params", params);
            
            // 发送请求
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = objectMapper.writeValueAsBytes(requestBody);
                os.write(input, 0, input.length);
            }
            
            // 读取响应
            int responseCode = connection.getResponseCode();
            System.out.println("初始化请求响应码: " + responseCode);
            
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = br.readLine()) != null) {
                        response.append(line);
                    }
                    
                    // 解析响应获取会话ID
                    ObjectNode jsonResponse = (ObjectNode) objectMapper.readTree(response.toString());
                    if (jsonResponse.has("result") && jsonResponse.get("result").has("session")) {
                        return jsonResponse.get("result").get("session").asText();
                    } else {
                        System.err.println("响应中未找到会话ID: " + response);
                        return null;
                    }
                }
            } else {
                System.err.println("初始化请求失败，响应码: " + responseCode);
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        System.err.println(line);
                    }
                }
                return null;
            }
            
        } catch (Exception e) {
            System.err.println("初始化MCP会话异常: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 测试简单的MCP流式工具
     */
    private static void testSimpleMcpStreamTool(String sessionId) {
        System.out.println("\n===== 测试简单MCP流式工具 =====");
        
        try {
            URL url = new URL(BASE_URL + MESSAGE_ENDPOINT + "?session=" + sessionId);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "text/event-stream");
            connection.setDoOutput(true);
            
            // 创建工具调用请求
            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("id", UUID.randomUUID().toString());
            requestBody.put("method", "tools/call");
            
            ObjectNode params = objectMapper.createObjectNode();
            params.put("tool", "simpleMcpStreamTool");
            
            ObjectNode arguments = objectMapper.createObjectNode();
            arguments.put("message", "这是一个流式测试消息！");
            params.set("arguments", arguments);
            
            requestBody.set("params", params);
            
            // 发送请求
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = objectMapper.writeValueAsBytes(requestBody);
                os.write(input, 0, input.length);
            }
            
            // 读取流式响应
            int responseCode = connection.getResponseCode();
            System.out.println("简单工具请求响应码: " + responseCode);
            
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        if (line.startsWith("data:")) {
                            String data = line.substring(5).trim();
                            System.out.println("收到流式数据: " + data);
                        }
                    }
                }
            } else {
                System.err.println("简单工具请求失败，响应码: " + responseCode);
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        System.err.println(line);
                    }
                }
            }
            
        } catch (Exception e) {
            System.err.println("测试简单MCP流式工具异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试完整的MCP流式工具
     */
    private static void testFullMcpStreamTool(String sessionId) {
        System.out.println("\n===== 测试完整MCP流式工具 =====");
        
        try {
            URL url = new URL(BASE_URL + MESSAGE_ENDPOINT + "?session=" + sessionId);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "text/event-stream");
            connection.setDoOutput(true);
            
            // 创建工具调用请求
            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("id", UUID.randomUUID().toString());
            requestBody.put("method", "tools/call");
            
            ObjectNode params = objectMapper.createObjectNode();
            params.put("tool", "testMcpStreamTool");
            
            ObjectNode arguments = objectMapper.createObjectNode();
            arguments.put("testMessage", "这是一个完整的流式测试！");
            arguments.put("chunkCount", "5");
            arguments.put("delayMs", "300");
            params.set("arguments", arguments);
            
            requestBody.set("params", params);
            
            // 发送请求
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = objectMapper.writeValueAsBytes(requestBody);
                os.write(input, 0, input.length);
            }
            
            // 读取流式响应
            int responseCode = connection.getResponseCode();
            System.out.println("完整工具请求响应码: " + responseCode);
            
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        if (line.startsWith("data:")) {
                            String data = line.substring(5).trim();
                            System.out.println("收到流式数据: " + data);
                        }
                    }
                }
            } else {
                System.err.println("完整工具请求失败，响应码: " + responseCode);
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        System.err.println(line);
                    }
                }
            }
            
        } catch (Exception e) {
            System.err.println("测试完整MCP流式工具异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}