package com.jd.jdt.joylaw.ai.mcp;

public class SimilarTextExtractor4 {

    public static void main(String[] args) {
        String A1 = "基本介绍\n\nUmo Editor 是一个基于 Vue3 和 Tiptap 的本土化开源文档编辑器，专为国人用户设计。它提供了强大的文档编辑能力和 AI 创作功能，支持分页模式、Markdown 语法、富文本编辑、多种格式的节点插入、页面样式设置、文档导出与打印等功能。此外，Umo Editor 还支持自定义扩展、多语言设置和暗色主题。\n\nUmo Editor 最大的特点是代码完全开源且自主可控，支持私有部署，您可以内网环境中使用，而无需担心数据安全问题。同时 Umo Editor 基于 Vue3 和 Tiptap，两者都有丰富的生态系统和社区支持，在遇到问题时可以迅速得到解决。\n\n作为一个独立的 Vue3 插件，Umo Editor 可以轻松集成到各类 Vue3 项目中。对于非 Vue3 项目，您可以通过 Iframe 将 Umo Editor 嵌入到您的项目中。\n\n开发文档 | 在线演示 | GitHub | 码云(国内镜像) | NPM\n\n在线体验\n\n访问 https://demo.umodoc.com/editor?pane=hide 快速体验。\n\n开发文档\n\n请访问 https://editor.umodoc.com/docs 。\n\n设计理念\n\nUmo Editor 的诞生旨在解决 Web 应用中文档编辑的复杂性，为 Web 项目提供类似 Microsoft Word 的强大编辑能力，同时保持 Web 应用的便捷性。无论是政企信息管理系统、学术研究撰写、团队文档协作、知识库管理还是个人笔记整理，Umo Editor 都能成为您的得力助手。\n\n开源优势\n\n\n\n\n\n免费使用：Umo Editor 基于 MIT 许可证 对所有开发者免费开放，无需担心版权问题。\n\n\n\n持续更新：Umo Editor 将持续迭代，不断优化功能，提升用户体验。\n\n\n\n定制化开发：开源意味着更大的灵活性，开发者可根据项目需求进行定制化开发，打造专属的文档编辑器。\n\n核心特性\n\n\n\n\n\n支持内网部署\n\n\n\n零配置开箱即用\n\n\n\n类似于与 Microsoft Word 的分页模式\n\n\n\n轻量级\n\n\n\n支持自定义扩展\n\n\n\n全过程所见即所得\n\n\n\n富文本编辑功能\n\n\n\nMarkdown 语法支持\n\n\n\n实用工具集成\n\n\n\n演示模式\n\n\n\n文档导出与分享\n\n\n\n页面设置\n\n\n\nAI 文档助手\n\n\n\n支持打印及打印预览\n\n\n\n气泡菜单与块级菜单\n\n\n\n快捷键支持\n\n\n\n主题定制\n\n\n\n多语言支持\n\n\n\n暗色主题\n\n更多详细介绍见 核心特性。\n\n浏览器支持\n\n\n\n\n\n\n\n浏览器\n\n\n\n版本\n\n\n\n支持情况\n\n\n\n\n\nGoogle Chrome\n\n\n\n最新版\n\n\n\n✅ 支持\n\n\n\n\n\nFirefox\n\n\n\n最新版\n\n\n\n✅ 支持\n\n\n\n\n\nSafari\n\n\n\n最新版\n\n\n\n✅ 支持\n\n\n\n\n\nMicrosoft Edge\n\n\n\n最新版\n\n\n\n✅ 支持\n\n\n\n\n\n360 极速浏览器\n\n\n\n最新版\n\n\n\n✅ 支持\n\n\n\n\n\n各类国产浏览器的极速模式\n\n\n\n最新版\n\n\n\n✅ 支持\n\n\n\n\n\nInternet Explorer (IE)\n\n\n\n所有\n\n\n\n❌ 不支持\n\n环境支持\n\n\n\n\n\nNode.js (>=v18.0.0)\n\n\n\nVue (>=v3.x)\n\n\n\nVite (>=v4.x)\n\n\n\nTiptap (>=v2.6)\n\n\n\nTypeScript (>=v5.5)\n\n加入社区\n\n我们鼓励用户加入 Umo Editor 的开源社区，共同参与到产品的开发和改进中。无论是提交 Bug 报告、功能请求还是代码贡献，都是我们社区宝贵的一部分。\n\n您可以通过 https://github.com/umodoc/editor/discussions 提交问题或意见。\n\n或通过 https://github.com/umodoc/editor/issues 提交 Bug 报告。\n\n贡献代码\n\nUmo Editor 的发展，离不开社区的支持，以下是为 Umo Editor 贡献过代码的贡献者名单，向他们致谢：\n\n\n\n\n\nUmo Team: 👨‍💻 核心开发者\n\n\n\nCassielxd: 💪🏻 实现了分页和许多重要功能\n\n\n\nchina-wangxu: 💪🏻 添加了许多重要功能\n\n\n\nNa'aman Hirschfeld: 💪🏻 增强对 TypeScript 的支持，添加测试，为 Umo Editor 的发展提供更好的基础\n\n\n\nChenErik: 🛠️ 为 Umo Editor 贡献了部分代码\n\n\n\nSerRashin: 🛠️ 为 Umo Editor 添加了俄语支持\n\n\n\nSunny Wisozk：🛠️ 贡献了部分代码\n\n\n\nxuzhenjun130: 🛠️ 为 Umo Editor 贡献了部分代码\n\n我们欢迎任何形式的贡献，包括但不限于提交 Bug 报告、功能请求、代码贡献等。\n\n联系我们\n\n如果您有任何疑问或建议，请通过以下方式联系我们。在此之前，建议您详细阅读本文档，以便了解如何使用 Umo Editor。\n\n\n\n\n\n反馈：https://github.com/umodoc/editor/issues\n\n\n\n社区：https://github.com/umodoc/editor/discussions\n\n\n\n邮件：<EMAIL>\n\n支持我们\n\n如果您觉得 Umo Editor 有用，请考虑通过以下方式支持我们：\n\n\n\n\n\n⭐ 给 Umo Editor 仓库 点个 Star，表示对项目的支持。\n\n\n\n🔗 如果您在项目中使用了 Umo Editor，请添加一个链接到 https://github.com/umodoc/editor 。\n\n定制开发\n\n如果您需要定制化开发，请联系我们，我们可以提供付费的定制化解决方案。详细信息请访问 定制开发。\n\n开源协议\n\nUmo Editor 采用 MIT 许可证，您可以免费使用、修改和使用本软件，但这不代表您可以随意删除版权信息，请保留 Umo Editor 的版权信息和界面上的链接地址，否则视为侵权，请支持开源项目。\n\n如果您不想保留版权信息，请联系我们或者购买商业版本 Umo Editor Next。\n\n本文档采用 CC BY-NC-SA 4.0 DEED 许可证 发布。";

        String B1 = "Umo Editor 还支持自定义扩展、多语言设置和暗色主题。\n\nUmo 最大的特点是代码完全开源且自主可控，支持私有部署，您可以内网环境中使用，而无需担心数据安全问题。同时 Umo Editor 基于 Vue3 和 Tiptap，两者都有丰富的生态系统和社区支持，在遇到问题时可以迅速得到解决。\n\n作为一个独立的 Vue3 插件插入\n\n，Umo Editor 可以轻松集成到各类 Vue3 项目中。";
        String s = extractMostSimilar(A1, B1);
        System.out.println(s);
        System.err.println("匹配内容长度:"+B1.length());
        System.err.println("匹配出来的原文长度:"+s.length());
    }

    public static String extractMostSimilar(String A, String B) {
        if (B.isEmpty()) return "";
        int lenA = A.length();
        int lenB = B.length();

        int minDistance = Integer.MAX_VALUE;
        String bestSub = "";
        int bestEnd = -1; // 记录最佳子串在A中的结束位置

        // 核心逻辑：遍历B的每个可能末尾位置（对齐到A的可能位置）
        // 优先让子串的结束位置接近B的末尾（lenB-1）
        for (int bEnd = lenB - 1; bEnd >= 0; bEnd--) {
            // 计算该bEnd对应的A中的可能结束位置（优先靠近A末尾）
            int aEnd = Math.min(lenA - 1, (lenA * (bEnd + 1)) / lenB); // 比例对齐（避免越界）
            aEnd = Math.max(aEnd, bEnd - (lenA - 1)); // 确保aEnd不小于0（极端情况）

            // 遍历A中以aEnd为中心的窗口（覆盖可能的子串结束位置）
            for (int aEndWindow = Math.max(0, aEnd - 5); aEndWindow <= Math.min(lenA - 1, aEnd + 5); aEndWindow++) {
                // 子串长度为：从aStart到aEndWindow的长度
                int subLength = aEndWindow + 1; // 子串长度=结束位置+1（索引从0开始）
                if (subLength > lenA) continue; // 防止越界

                // 子串起始位置
                int aStart = aEndWindow - (subLength - 1);
                if (aStart < 0) {
                    aStart = 0;
                    subLength = aEndWindow + 1; // 调整长度
                }

                String sub = A.substring(aStart, aEndWindow + 1);
                int distance = editDistance(sub, B);

                // 更新最优解：优先更小的编辑距离，其次结束位置更接近B末尾
                if (distance < minDistance ||
                        (distance == minDistance && (aEndWindow - (subLength - 1)) > (bestEnd - (bestSub.length() - 1)))) {
                    minDistance = distance;
                    bestSub = sub;
                    bestEnd = aEndWindow;
                }
            }
        }

        // 兜底：若未找到（理论上不会），返回A的前min(lenA, lenB)字符
        return bestSub.isEmpty() ? A.substring(0, Math.min(lenA, lenB)) : bestSub;
    }

    // 标准Levenshtein编辑距离（用于精确计算）
    private static int editDistance(String s1, String s2) {
        int[][] dp = new int[s1.length() + 1][s2.length() + 1];
        for (int i = 0; i <= s1.length(); i++) dp[i][0] = i;
        for (int j = 0; j <= s2.length(); j++) dp[0][j] = j;

        for (int i = 1; i <= s1.length(); i++) {
            for (int j = 1; j <= s2.length(); j++) {
                int cost = (s1.charAt(i - 1) == s2.charAt(j - 1)) ? 0 : 1;
                dp[i][j] = Math.min(
                        Math.min(dp[i - 1][j] + 1, dp[i][j - 1] + 1),
                        dp[i - 1][j - 1] + cost
                );
            }
        }
        return dp[s1.length()][s2.length()];
    }
}