package com.jd.jdt.joylaw.ai.mcp;

import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.util.UUID;

/**
 * <p>
 * MCP流式功能测试类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Log4j2
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class McpStreamingTest {

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    /**
     * 测试MCP初始化端点
     */
    @Test
    public void testMcpInitialize() {
        log.info("开始测试MCP初始化端点");
        
        String url = "http://localhost:" + port + "/mcp/initialize";
        
        // 构建请求体
        String requestBody = String.format(
                "{\"jsonrpc\":\"2.0\",\"id\":\"%s\",\"method\":\"initialize\",\"params\":{\"protocol\":\"2024-11-05\"}}",
                UUID.randomUUID().toString());
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<String> request = new HttpEntity<>(requestBody, headers);
        
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
        
        log.info("MCP初始化响应: {}", response.getBody());
        log.info("MCP初始化状态码: {}", response.getStatusCode());
    }

    /**
     * 测试简单流式端点
     */
    @Test
    public void testStreamEndpoint() {
        log.info("开始测试简单流式端点");
        
        String url = "http://localhost:" + port + "/mcp/stream-test";
        
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        
        log.info("流式端点响应: {}", response.getBody());
        log.info("流式端点状态码: {}", response.getStatusCode());
    }
}