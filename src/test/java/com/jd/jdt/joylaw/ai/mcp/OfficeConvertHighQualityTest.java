package com.jd.jdt.joylaw.ai.mcp;

import com.jd.jdt.joylaw.ai.mcp.enums.AsposeConvertTypeEnum;
import com.jd.jdt.joylaw.ai.mcp.pojo.dto.AsposeConvertDTO;
import com.jd.jdt.joylaw.ai.mcp.pojo.vo.AsposeConvertVO;
import com.jd.jdt.joylaw.ai.mcp.service.AsposeConvertRule;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 高质量Word转PDF测试类 zhaohan
 * 用于验证格式保持效果
 */
@Log4j2
@SpringBootTest
public class OfficeConvertHighQualityTest {

    @Autowired
    private AsposeConvertRule asposeConvertRule;

    @Test
    public void testHighQualityWordToPdfConversion() throws IOException {
        // 1. 读取测试Word文件
        String inputPath = "src/main/resources/static/【样例】技术服务协议模板（jd为销售方通用版）.docx";
        File inputFile = new File(inputPath);
        
        if (!inputFile.exists()) {
            log.warn("测试文件不存在: {}", inputPath);
            return;
        }
        
        byte[] fileBytes = Files.readAllBytes(inputFile.toPath());
        log.info("读取Word文件成功，文件大小: {} bytes", fileBytes.length);

        // 2. 构造转换请求
        AsposeConvertDTO dto = new AsposeConvertDTO();
        dto.setFileName(inputFile.getName());
        dto.setByteArray(fileBytes);
        dto.setAsposeConvertType(AsposeConvertTypeEnum.DOCX_TO_PDF);

        // 3. 执行高质量转换
        long startTime = System.currentTimeMillis();
        AsposeConvertVO vo = asposeConvertRule.executeConvertRule(dto);
        long endTime = System.currentTimeMillis();
        
        log.info("转换完成，耗时: {} ms", endTime - startTime);
        log.info("转换后PDF文件大小: {} bytes", vo.getByteArray().length);

        // 4. 保存转换结果
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String outputPath = "src/main/resources/static/" + timestamp + AsposeConvertTypeEnum.DOCX_TO_PDF.getTargetSuffix();
        
        // 确保输出目录存在
        File outputFile = new File(outputPath);
        outputFile.getParentFile().mkdirs();
        
        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
            fos.write(vo.getByteArray());
            log.info("高质量PDF文件已保存到: {}", outputPath);
        }

        // 5. 验证转换结果
        assert vo.getByteArray().length > 0 : "转换后的PDF文件不能为空";
        assert vo.getFileName().endsWith(".pdf") : "输出文件名应该以.pdf结尾";
        
        log.info("=== 高质量转换测试完成 ===");
        log.info("请检查输出文件: {} 以验证格式保持效果", outputPath);
        log.info("预期效果: 字体、加粗、颜色、表格边框等格式应完美还原");
    }

    @Test
    public void testCompareConversionQuality() throws IOException {
        // 此测试用于对比原始转换和高质量转换的效果差异
        String inputPath = "src/main/resources/static/【样例】技术服务协议模板（jd为销售方通用版）.docx";
        File inputFile = new File(inputPath);
        
        if (!inputFile.exists()) {
            log.warn("测试文件不存在: {}", inputPath);
            return;
        }
        
        byte[] fileBytes = Files.readAllBytes(inputFile.toPath());
        
        // 构造转换请求
        AsposeConvertDTO dto = new AsposeConvertDTO();
        dto.setFileName(inputFile.getName());
        dto.setByteArray(fileBytes);
        dto.setAsposeConvertType(AsposeConvertTypeEnum.DOCX_TO_PDF);

        // 执行转换
        AsposeConvertVO vo = asposeConvertRule.executeConvertRule(dto);
        
        // 保存结果用于人工对比
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String outputPath = "src/main/resources/static/" + timestamp + "2.pdf";
        
        File outputFile = new File(outputPath);
        outputFile.getParentFile().mkdirs();
        
        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
            fos.write(vo.getByteArray());
            log.info("质量对比PDF文件已保存到: {}", outputPath);
        }
        
        log.info("=== 质量对比测试完成 ===");
        log.info("请将此文件与原Word文档进行对比，检查以下格式保持情况:");
        log.info("1. 字体类型和大小");
        log.info("2. 文字加粗、斜体、下划线");
        log.info("3. 文字颜色和背景色");
        log.info("4. 表格边框和单元格样式");
        log.info("5. 段落间距和行距");
        log.info("6. 页眉页脚");
        log.info("7. 图片质量和位置");
    }
}
