package com.jd.jdt.joylaw.ai.mcp;

import com.aspose.words.SaveFormat;

/**
 * <p>
 * SaveFormat 枚举值获取程序
 * 用于获取 Aspose Words SaveFormat 的具体数值
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
public class SaveFormatMain {

    public static void main(String[] args) {
        System.out.println("=== Aspose Words SaveFormat 枚举值 ===");
        
        // 文本格式
        System.out.println("文本格式:");
        System.out.println("SaveFormat.TEXT = " + SaveFormat.TEXT);
        System.out.println("SaveFormat.RTF = " + SaveFormat.RTF);
        
        // 文档格式
        System.out.println("\n文档格式:");
        System.out.println("SaveFormat.DOCX = " + SaveFormat.DOCX);
        System.out.println("SaveFormat.DOC = " + SaveFormat.DOC);
        System.out.println("SaveFormat.ODT = " + SaveFormat.ODT);
        
        // Web格式
        System.out.println("\nWeb格式:");
        System.out.println("SaveFormat.HTML = " + SaveFormat.HTML);
        System.out.println("SaveFormat.MHTML = " + SaveFormat.MHTML);
        System.out.println("SaveFormat.EPUB = " + SaveFormat.EPUB);
        
        // PDF和其他格式
        System.out.println("\nPDF和其他格式:");
        System.out.println("SaveFormat.PDF = " + SaveFormat.PDF);
        System.out.println("SaveFormat.XPS = " + SaveFormat.XPS);
        
        // 图片格式
        System.out.println("\n图片格式:");
        System.out.println("SaveFormat.JPEG = " + SaveFormat.JPEG);
        System.out.println("SaveFormat.PNG = " + SaveFormat.PNG);
        System.out.println("SaveFormat.TIFF = " + SaveFormat.TIFF);
        System.out.println("SaveFormat.BMP = " + SaveFormat.BMP);
        System.out.println("SaveFormat.SVG = " + SaveFormat.SVG);
        
        // 表格格式（如果支持）
        System.out.println("\n表格格式:");
        try {
            System.out.println("SaveFormat.XLSX = " + SaveFormat.XLSX);
        } catch (Exception e) {
            System.out.println("SaveFormat.XLSX 不支持");
        }
        
        // 其他格式
        System.out.println("\n其他格式:");
        try {
            System.out.println("SaveFormat.MARKDOWN = " + SaveFormat.MARKDOWN);
        } catch (Exception e) {
            System.out.println("SaveFormat.MARKDOWN 不支持");
        }
        
        try {
            System.out.println("SaveFormat.XAML_FLOW = " + SaveFormat.XAML_FLOW);
        } catch (Exception e) {
            System.out.println("SaveFormat.XAML_FLOW 不支持");
        }
        
        try {
            System.out.println("SaveFormat.XAML_FLOW_PACK = " + SaveFormat.XAML_FLOW_PACK);
        } catch (Exception e) {
            System.out.println("SaveFormat.XAML_FLOW_PACK 不支持");
        }
    }
}