package com.jd.jdt.joylaw.ai.mcp;

import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

/**
 * 简单的MCP流式测试客户端
 * 用于测试MCP服务器的流式返回功能
 */
public class StreamingTestClient {

    private static final String BASE_URL = "http://localhost:8081";
    private static final String MCP_ENDPOINT = "/mcp";

    public static void main(String[] args) {
        System.out.println("开始测试MCP流式功能...");
        
        // 创建WebClient
        WebClient webClient = WebClient.builder()
                .baseUrl(BASE_URL)
                .build();
        
        // 测试简单流式工具
        testSimpleMcpStreamTool(webClient);
        
        // 等待一段时间以确保所有响应都被处理
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 测试简单的MCP流式工具
     */
    private static void testSimpleMcpStreamTool(WebClient webClient) {
        System.out.println("\n===== 测试简单MCP流式工具 =====");
        
        String requestBody = "{\n" +
                "  \"tool\": \"simpleMcpStreamTool\",\n" +
                "  \"arguments\": {\n" +
                "    \"message\": \"这是一个流式测试消息！\"\n" +
                "  }\n" +
                "}";
        
        webClient.post()
                .uri(MCP_ENDPOINT)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(requestBody)
                .retrieve()
                .bodyToFlux(String.class)
                .doOnNext(chunk -> {
                    System.out.println("收到数据块: " + chunk);
                })
                .doOnComplete(() -> {
                    System.out.println("简单MCP流式测试完成");
                })
                .doOnError(error -> {
                    System.err.println("简单MCP流式测试出错: " + error.getMessage());
                })
                .subscribe();
    }
    
    /**
     * 测试完整的MCP流式工具
     */
    private static void testFullMcpStreamTool(WebClient webClient) {
        System.out.println("\n===== 测试完整MCP流式工具 =====");
        
        String requestBody = "{\n" +
                "  \"tool\": \"testMcpStreamTool\",\n" +
                "  \"arguments\": {\n" +
                "    \"testMessage\": \"这是一个完整的流式测试！\",\n" +
                "    \"chunkCount\": \"5\",\n" +
                "    \"delayMs\": \"300\"\n" +
                "  }\n" +
                "}";
        
        webClient.post()
                .uri(MCP_ENDPOINT)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(requestBody)
                .retrieve()
                .bodyToFlux(String.class)
                .doOnNext(chunk -> {
                    System.out.println("收到数据块: " + chunk);
                })
                .doOnComplete(() -> {
                    System.out.println("完整MCP流式测试完成");
                })
                .doOnError(error -> {
                    System.err.println("完整MCP流式测试出错: " + error.getMessage());
                })
                .subscribe();
    }
}