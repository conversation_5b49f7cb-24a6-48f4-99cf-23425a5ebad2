package com.jd.jdt.joylaw.ai.mcp;

import com.jd.jdt.joylaw.ai.mcp.enums.AsposeConvertTypeEnum;
import com.jd.jdt.joylaw.ai.mcp.pojo.dto.AsposeConvertDTO;
import com.jd.jdt.joylaw.ai.mcp.pojo.vo.AsposeConvertVO;
import com.jd.jdt.joylaw.ai.mcp.service.AsposeConvertService;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Word转PDF质量验证测试
 * 专门用于验证字体和格式保持效果
 */
@Log4j2
@SpringBootTest
public class OfficeConvertQualityVerificationTest {

    @Autowired
    private AsposeConvertService asposeConvertService;

    @Test
    public void testWordToPdfQualityVerification() throws IOException {
        log.info("=== 开始Word转PDF质量验证测试 ===");
        
        // 1. 读取测试Word文件
        String inputPath = "src/main/resources/static/【样例】技术服务协议模板（jd为销售方通用版）.docx";
        File inputFile = new File(inputPath);
        
        if (!inputFile.exists()) {
            log.warn("测试文件不存在: {}", inputPath);
            return;
        }
        
        byte[] fileBytes = Files.readAllBytes(inputFile.toPath());
        log.info("✓ 读取Word文件成功，文件大小: {} bytes", fileBytes.length);

        // 2. 构造转换请求
        AsposeConvertDTO dto = new AsposeConvertDTO();
        dto.setFileName(inputFile.getName());
        dto.setByteArray(fileBytes);
        dto.setAsposeConvertType(AsposeConvertTypeEnum.DOCX_TO_PDF);

        // 3. 执行高质量转换
        log.info("开始执行高质量Word转PDF转换...");
        long startTime = System.currentTimeMillis();
        
        AsposeConvertVO vo = asposeConvertService.executeDocxToPdfConversion(dto);
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        log.info("✓ 转换完成！");
        log.info("  - 转换耗时: {} ms", duration);
        log.info("  - 原文件大小: {} bytes", fileBytes.length);
        log.info("  - PDF文件大小: {} bytes", vo.getByteArray().length);
        log.info("  - 文件大小比率: {:.2f}%", (double) vo.getByteArray().length / fileBytes.length * 100);

        // 4. 保存转换结果
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String outputPath = "target/test-output/quality_verification_" + timestamp + ".pdf";
        
        // 确保输出目录存在
        File outputFile = new File(outputPath);
        outputFile.getParentFile().mkdirs();
        
        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
            fos.write(vo.getByteArray());
            log.info("✓ 高质量PDF文件已保存到: {}", outputPath);
        }

        // 5. 验证转换结果
        assert vo.getByteArray().length > 0 : "转换后的PDF文件不能为空";
        assert vo.getFileName().endsWith(".pdf") : "输出文件名应该以.pdf结尾";
        assert vo.getByteArray().length > 1000 : "PDF文件大小应该合理";
        
        log.info("=== 质量验证测试完成 ===");
        log.info("请手动检查输出文件: {}", outputPath);
        printQualityCheckList();
    }

    @Test
    public void testMultipleDocumentConversion() throws IOException {
        log.info("=== 开始批量文档转换测试 ===");
        
        // 测试多个文档的转换效果
        String[] testFiles = {
            "src/main/resources/static/【样例】技术服务协议模板（jd为销售方通用版）.docx"
        };
        
        for (String filePath : testFiles) {
            File inputFile = new File(filePath);
            if (!inputFile.exists()) {
                log.warn("测试文件不存在: {}", filePath);
                continue;
            }
            
            log.info("处理文件: {}", inputFile.getName());
            
            byte[] fileBytes = Files.readAllBytes(inputFile.toPath());
            
            AsposeConvertDTO dto = new AsposeConvertDTO();
            dto.setFileName(inputFile.getName());
            dto.setByteArray(fileBytes);
            dto.setAsposeConvertType(AsposeConvertTypeEnum.DOCX_TO_PDF);
            
            long startTime = System.currentTimeMillis();
            AsposeConvertVO vo = asposeConvertService.executeDocxToPdfConversion(dto);
            long endTime = System.currentTimeMillis();
            
            // 保存结果
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String outputPath = "target/test-output/batch_" + inputFile.getName().replace(".docx", "") + "_" + timestamp + ".pdf";
            
            File outputFile = new File(outputPath);
            outputFile.getParentFile().mkdirs();
            
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                fos.write(vo.getByteArray());
            }
            
            log.info("✓ 文件转换完成: {} -> {}", inputFile.getName(), outputFile.getName());
            log.info("  - 耗时: {} ms", endTime - startTime);
            log.info("  - 大小: {} -> {} bytes", fileBytes.length, vo.getByteArray().length);
        }
        
        log.info("=== 批量转换测试完成 ===");
    }

    @Test
    public void testConversionPerformance() throws IOException {
        log.info("=== 开始转换性能测试 ===");
        
        String inputPath = "src/main/resources/static/【样例】技术服务协议模板（jd为销售方通用版）.docx";
        File inputFile = new File(inputPath);
        
        if (!inputFile.exists()) {
            log.warn("测试文件不存在: {}", inputPath);
            return;
        }
        
        byte[] fileBytes = Files.readAllBytes(inputFile.toPath());
        
        // 执行多次转换测试性能
        int testRounds = 3;
        long totalTime = 0;
        
        for (int i = 1; i <= testRounds; i++) {
            log.info("执行第 {} 轮转换测试", i);
            
            AsposeConvertDTO dto = new AsposeConvertDTO();
            dto.setFileName(inputFile.getName());
            dto.setByteArray(fileBytes);
            dto.setAsposeConvertType(AsposeConvertTypeEnum.DOCX_TO_PDF);
            
            long startTime = System.currentTimeMillis();
            AsposeConvertVO vo = asposeConvertService.executeDocxToPdfConversion(dto);
            long endTime = System.currentTimeMillis();
            
            long duration = endTime - startTime;
            totalTime += duration;
            
            log.info("  - 第 {} 轮耗时: {} ms", i, duration);
            log.info("  - 输出大小: {} bytes", vo.getByteArray().length);
        }
        
        long averageTime = totalTime / testRounds;
        log.info("=== 性能测试结果 ===");
        log.info("  - 测试轮数: {}", testRounds);
        log.info("  - 总耗时: {} ms", totalTime);
        log.info("  - 平均耗时: {} ms", averageTime);
        log.info("  - 文件大小: {} bytes", fileBytes.length);
        
        // 性能断言
        assert averageTime < 10000 : "平均转换时间应该在10秒以内";
        
        log.info("=== 性能测试完成 ===");
    }
    
    /**
     * 打印质量检查清单
     */
    private void printQualityCheckList() {
        log.info("");
        log.info("=== PDF质量检查清单 ===");
        log.info("请手动验证以下格式保持情况:");
        log.info("□ 1. 中文字体显示正确（宋体、黑体、微软雅黑等）");
        log.info("□ 2. 英文字体显示正确（Times New Roman、Arial、Calibri等）");
        log.info("□ 3. 文字加粗、斜体、下划线效果保持");
        log.info("□ 4. 文字颜色和背景色正确");
        log.info("□ 5. 表格边框和单元格样式完整");
        log.info("□ 6. 段落间距和行距保持一致");
        log.info("□ 7. 页眉页脚内容完整");
        log.info("□ 8. 图片质量清晰，位置正确");
        log.info("□ 9. 页面布局与原文档一致");
        log.info("□ 10. 超链接功能正常");
        log.info("□ 11. 书签和目录结构完整");
        log.info("□ 12. 整体视觉效果与Word文档一致");
        log.info("");
        log.info("如果以上所有项目都符合预期，说明格式保持修复成功！");
        log.info("=========================");
    }
}