package com.jd.jdt.joylaw.ai.mcp;

import com.aspose.words.Document;
import com.aspose.words.DocumentBuilder;
import com.aspose.words.SaveFormat;
import com.jd.jdt.joylaw.ai.mcp.enums.AsposeConvertTypeEnum;
import com.jd.jdt.joylaw.ai.mcp.pojo.dto.AsposeConvertDTO;
import com.jd.jdt.joylaw.ai.mcp.pojo.vo.AsposeConvertVO;
import com.jd.jdt.joylaw.ai.mcp.service.AsposeConvertService;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 简单的Word转PDF测试类
 * 创建测试文档并验证格式保持效果
 */
@Log4j2
@SpringBootTest
public class AsposeConvertServiceSimpleTest {

    @Autowired
    private AsposeConvertService asposeConvertService;

    @Test
    public void testCreateAndConvertWordToPdf() throws Exception {
        // 1. 创建一个包含各种格式的测试Word文档
//        byte[] wordBytes = createTestWordDocument();
        // 1. 读取测试Word文件
        String inputPath = "src/main/resources/static/中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx";
        File inputFile = new File(inputPath);

        if (!inputFile.exists()) {
            log.warn("测试文件不存在: {}", inputPath);
            return;
        }

        byte[] wordBytes = Files.readAllBytes(inputFile.toPath());
        log.info("创建测试Word文档成功，文件大小: {} bytes", wordBytes.length);

        // 2. 构造转换请求
        AsposeConvertDTO dto = new AsposeConvertDTO();
        dto.setFileName("test_document.docx");
        dto.setByteArray(wordBytes);
        dto.setAsposeConvertType(AsposeConvertTypeEnum.DOCX_TO_PDF);

        // 3. 执行高质量转换
        long startTime = System.currentTimeMillis();
        AsposeConvertVO vo = asposeConvertService.executeDocxToPdfConversion(dto);
        long endTime = System.currentTimeMillis();
        
        log.info("转换完成，耗时: {} ms", endTime - startTime);
        log.info("转换后PDF文件大小: {} bytes", vo.getByteArray().length);

        // 4. 保存转换结果
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String outputPath = "target/test-output/converted_" + timestamp + ".pdf";
        
        // 确保输出目录存在
        File outputFile = new File(outputPath);
        outputFile.getParentFile().mkdirs();
        
        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
            fos.write(vo.getByteArray());
            log.info("转换后的PDF文件已保存到: {}", outputPath);
        }

        // 5. 验证转换结果
        assert vo.getByteArray().length > 0 : "转换后的PDF文件不能为空";
        assert vo.getFileName().endsWith(".pdf") : "输出文件名应该以.pdf结尾";
        
        log.info("=== 高质量Word转PDF测试完成 ===");
        log.info("请检查输出文件: {} 以验证格式保持效果", outputPath);
        log.info("预期效果: 字体、加粗、颜色、表格等格式应完美还原");
    }

    /**
     * 创建一个包含各种格式的测试Word文档
     */
    private byte[] createTestWordDocument() throws Exception {
        Document doc = new Document();
        DocumentBuilder builder = new DocumentBuilder(doc);

        // 标题
        builder.getFont().setSize(18);
        builder.getFont().setBold(true);
        builder.getFont().setColor(java.awt.Color.BLUE);
        builder.writeln("Word转PDF格式保持测试文档");
        
        builder.getFont().setSize(12);
        builder.getFont().setBold(false);
        builder.getFont().setColor(java.awt.Color.BLACK);
        builder.writeln("");

        // 普通文本
        builder.writeln("这是普通文本段落，用于测试基本文字格式。");
        builder.writeln("");

        // 加粗文本
        builder.getFont().setBold(true);
        builder.write("这是加粗文本");
        builder.getFont().setBold(false);
        builder.write("，后面是普通文本。");
        builder.writeln("");
        builder.writeln("");

        // 斜体文本
        builder.getFont().setItalic(true);
        builder.write("这是斜体文本");
        builder.getFont().setItalic(false);
        builder.write("，后面是普通文本。");
        builder.writeln("");
        builder.writeln("");

        // 下划线文本
        builder.getFont().setUnderline(com.aspose.words.Underline.SINGLE);
        builder.write("这是下划线文本");
        builder.getFont().setUnderline(com.aspose.words.Underline.NONE);
        builder.write("，后面是普通文本。");
        builder.writeln("");
        builder.writeln("");

        // 彩色文本
        builder.getFont().setColor(java.awt.Color.RED);
        builder.write("这是红色文本");
        builder.getFont().setColor(java.awt.Color.GREEN);
        builder.write("，这是绿色文本");
        builder.getFont().setColor(java.awt.Color.BLACK);
        builder.write("，这是黑色文本。");
        builder.writeln("");
        builder.writeln("");

        // 不同字体大小
        builder.getFont().setSize(14);
        builder.write("这是14号字体");
        builder.getFont().setSize(16);
        builder.write("，这是16号字体");
        builder.getFont().setSize(12);
        builder.write("，这是12号字体。");
        builder.writeln("");
        builder.writeln("");

        // 创建表格
        builder.writeln("下面是一个测试表格：");
        builder.startTable();
        
        // 表头
        builder.insertCell();
        builder.getFont().setBold(true);
        builder.write("姓名");
        builder.insertCell();
        builder.write("年龄");
        builder.insertCell();
        builder.write("职位");
        builder.endRow();
        
        // 数据行
        builder.getFont().setBold(false);
        builder.insertCell();
        builder.write("张三");
        builder.insertCell();
        builder.write("25");
        builder.insertCell();
        builder.write("工程师");
        builder.endRow();
        
        builder.insertCell();
        builder.write("李四");
        builder.insertCell();
        builder.write("30");
        builder.insertCell();
        builder.write("经理");
        builder.endRow();
        
        builder.endTable();
        builder.writeln("");

        // 项目符号列表
        builder.writeln("项目符号列表：");
        builder.getListFormat().applyBulletDefault();
        builder.writeln("第一项内容");
        builder.writeln("第二项内容");
        builder.writeln("第三项内容");
        builder.getListFormat().removeNumbers();
        builder.writeln("");

        // 保存为字节数组
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        doc.save(out, SaveFormat.DOCX);
        return out.toByteArray();
    }
}
