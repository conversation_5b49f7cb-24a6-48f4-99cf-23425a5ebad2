package com.jd.jdt.joylaw.ai.mcp;

import com.alibaba.fastjson2.JSON;
import com.jd.jdt.joylaw.ai.mcp.pojo.dto.DoronChatDTO;
import com.jd.jdt.joylaw.ai.mcp.pojo.dto.DoronChatMessageDTO;
import com.jd.jdt.joylaw.ai.mcp.rpc.feign.JoyEditService;
import feign.Response;
import lombok.extern.log4j.Log4j2;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

@Log4j2
@RunWith(SpringRunner.class)
@SpringBootTest(classes = JoyLawMcpServerApplication.class)
public class Testf {


    @Autowired
    private JoyEditService joyEditService;


    @Test
    public void videosResult2() {
        DoronChatDTO doronChatDTO = new DoronChatDTO();
        doronChatDTO.setSessionId("LS1111111111");
        List<DoronChatMessageDTO> doronChatDTOList = new ArrayList<>();
        DoronChatMessageDTO doronChatMessageDTO = new DoronChatMessageDTO();
        doronChatMessageDTO.setChatRole("USER");
//        doronChatMessageDTO.setContent("写一份销售合同文档");
        doronChatDTOList.add(doronChatMessageDTO);
        doronChatDTO.setMessages(doronChatDTOList);
        doronChatDTO.setStream(Boolean.FALSE);
        String object = joyEditService.chatDoronStream("sso.jd.com=BJ.2256B28DBE1A88D1CE5E74F1F852D7776020250603105717", doronChatDTO);
        log.info(object);
    }

}
