package com.jd.jdt.joylaw.ai.mcp;

public class SimilarTextExtractor2 {

    public static void main(String[] args) {
        // 测试案例1：B中间缺失，提取结果末尾对齐
        String A1 ="项目管理模块PRD 1.项目背景1.1市场需求当前市场中，企业对项目管理工具的需求显著增长，主要驱动因素包括数字化转型加速、远程协作常态化以及复杂项目交付周期的缩短。根据Gartner年报告，全球项目管理软件市场规模预计以12.8%的年复合增长率扩张，但现有解决方案仍存在以下核心痛点：1.跨部门协同低效：传统工具如Excel和邮件多角色实时协作需求，导致信息孤岛现象，某咨询公司调研显示42%的项目延误源于沟通成本过高；2.敏捷适配能力不足：中小企业需要同时支持传统瀑布式与敏捷，但市面产品往往强制要求单一方法论；3.数据洞察薄弱：62%的项目经理反馈现有工具缺乏自动化的风险预测和资源优化建议功能；4.集成成本高企CRM/ERP等企业系统的对接需要额外开发，某制造业客户案例显示集成开支可达软件采购费用的3倍。特别值得注意的是，医疗和金融行业因合规性要求，亟需具备审计追踪颗粒化管理能力的专属解决方案，这构成当前市场的重要空白点。1.2竞争分析通过对Jira、Asana、Microsoft Project等主流产品的基准发现以下竞争格局特征：产品核心优势显著缺陷典型用户画像Jira Software高度可定制的敏捷看板学习曲线陡峭（平均需14天培训）50人以上技术团队Asana的任务可视化界面缺乏资源管理模块Microsoft强大的甘特图和资源调度本地部署版本协作功能薄弱工程建设领域大型企业Wrike优秀的跨项目组合分析移动端体验差（用户评分2.8/5）跨国公司分布式团队新兴竞争者如ClickAll-in-One策略获得快速增长，但其系统稳定性问题导致23%的客户在试用期流失。当前市场呈现明显的分层现象：高端客户偏好定制化能力强的企业级方案，而SMB倾向即开即用的轻量级产品。1.3业务目标本模块旨在实现以下关键业务指标（KPI）：1.效率提升：将项目规划时间缩短40%，通过智能和自动化工作流实现；2.成本控制：降低30%的跨系统集成费用，内置预置连接器支持主流ERP/CRM系统；3.质量保障：达成98%的任务可，采用区块链技术确保关键节点数据不可篡改；4.市场渗透：在18个月内覆盖医疗行业15%的头部客户，通过HIPAA/GDPR合规认证；5.**收入增长形成阶梯式产品矩阵，预计企业版ARR贡献率将达总营收的65%。阶段性里程碑包括：Q3完成MVP核心功能开发，Q4通过ISO 27001安全认证，次年Q2实现Salesforce/SAP的标准API对接。所有目标设定均参照SMART原则，并设置月度OKR进行动态追踪。2.产品概述2.1核心功能项目管理模块旨在提供全生命周期的项目管控能力，支持从立项到结项的全流程数字化管理。核心功能包括：1.项目立项与规划支持多维度项目模板创建（研发/市场/基建等类型），可配置WBS工作分解结构、里程碑计划及甘特图o智能预算分配功能，支持人力成本、物料费用的弹性测算红线预警2.任务协同与执行o可视化看板（Kanban/列表/日历视图）动态展示任务状态，支持任务派发、转派、优先级标记自动化触发依赖任务流转，当前置任务完成率达100%时自动解锁后续任务3.资源调度监控o资源热力图显示成员负荷度，支持跨项目资源冲突智能调配建议o实时跟踪设备、场地等物理资源使用情况，预留冲突弹窗提醒4.风险与质量管控o内置FMEA风险库，自动关联项目数据生成风险概率矩阵o质量门禁检查点强制卡控，未通过验收则禁止进入下一阶段5.数据分析与报告o自动生成挣值分析VA）图表，动态计算SPI/CPI指标o定制化输出项目健康度雷达图，包含进度偏差、成本超支等6维评估指标典型使用场景：•研发类项目迭代计划跟踪敏捷开发进度，自动同步JIRA需求状态•工程类项目：结合BIM模型进行施工任务分解，关联监理验收单据2.2用户角色1.管理员**•负责模块权限分配与工作流配置•维护项目分类体系及全局模板库•关键操作：项目归档/恢复、数据备份2.（PM）**•创建并监控项目执行，审批阶段交付物•拥有任务强制干预权（暂停/终止/变更基准计划）•典型操作：发起项目变更、调整资源配比3.项目成员o执行具体任务并更新进度（支持PC端/移动端填报）o可发起风险登记与缺陷上报o：仅能查看参与项目的敏感数据4.质量专员（QA）o执行阶段质量审计，标记过程不符合项o特殊权限：质量门禁的强制驳回5.高层管理者o查看项目组合仪表盘，进行战略资源调配o核心功能：多项目对比分析、投资回报率预测3.功能需求3.1项目跟踪项目管理模块通过以下功能实现项目进度和状态的实时跟踪：1.甘特图与时间轴视图：支持动态展示项目里程碑、关键路径及任务，时间轴可精确到天/小时，支持拖拽调整。2.状态看板：采用敏捷看板（如To-Do/In Progress/Done）或自定义状态流，实时反映任务完成与阻塞问题。3.自动化进度计算：基于任务完成量、工时消耗与预设权重，自动计算项目整体进度百分比，偏差超过阈值时触发预警。4.**集成监控与代码仓库（如Git）、CI/CD工具联动，自动关联提交记录与任务卡，确保开发进度透明化。3.2任务管理任务管理功能覆盖全具体实现如下：1.任务创建：o支持文本描述、附件上传、子任务拆分及关联需求编号（如Jira ID）。o可设置任务类型（/测试/设计）、预估工时与截止日期。2.分配与协作：o支持多级分配（负责人、参与人、审核人），自动通知相关人员。o内置@提及功能，讨论记录可追溯。3.优先级管理：o采用四象限法则（紧急/重要）或自定义标签（P0-P3），支持批量调整优先级。4.跟踪**：o成员每日更新任务状态（如完成50%），系统自动同步至关联项目进度。3.3资源管理资源管理功能聚焦于调配与成本控制：1.人力资源：o可视化团队负载仪表盘，展示成员当前任务量、技能匹配度，避免过载分配。o支持跨项目资源与审批流程。2.时间管理：o基于任务工时的日历视图，识别资源冲突时段，支持自动排期建议。3.预算控制：o系统，实时对比实际支出与预算（如人力成本、采购费用），超支时冻结相关任务创建。3.4报告生成系统提供多维度数据分析与自动化：1.定制化模板：o预置周报/月报模板，可自定义字段（如进度、风险、资源利用率）。2.数据源整合：o任务完成率、工时偏差、预算执行率等指标，支持图表混合编排（折线图+饼图）。3.智能分析：o自动标记异常数据（如进度滞后≥10%的任务关联根因建议（如资源不足）。4.分发与归档：o报告可导出为PDF/Excel，通过邮件或企业微信推送至干系人，并按项目分类存储。4.非功能需求4.1安全性1.身份认证与授权o采用基于角色的访问控制（RBAC）模型，支持多因素认证（MFA）及OAuth 2.0协议o敏感操作（如删除项目、修改权限）需二次验证，并记录审计日志。o权限粒度细化至模块功能级别，支持自定义角色权限配置。2.数据o数据传输使用TLS 1.2+加密，静态数据通过AES-256算法加密存储。o关键业务数据（如用户凭证、项目配置）实施脱敏处理，仅人员访问原始数据。o定期执行漏洞扫描与渗透测试，符合ISO 27001标准要求。3.审计与合规o全量记录用户操作日志（戳、操作内容、IP地址），留存至少180天。o支持SOC 2 Type II合规性报告自动生成，提供数据泄露应急响应预案。2性能1.负载能力o单节点支持并发用户数≥5000，集群模式下横向扩展至10万级并发。o核心接口（如任务创建、状态查询时间≤200ms（P99）。2.弹性与容灾o系统在CPU使用率≥80%时自动触发动态扩容，30秒内完成资源调配。o跨部署，故障切换RTO≤60秒，RPO=0。3.监控指标o定义关键指标：\uF0A7API吞吐量≥5000 QPS-查询延迟≤50ms\uF0A7每日定时任务完成率≥99.9%o提供Prometheus/Grafana监控看板，支持自定义告警阈值设置。5.用户界面设计5.1设计理念本模块采用“用户为中心，效率优先”的设计原则，通过以下核心理念实现项目管理的专业性与易用性：1.极简交互：减少冗余，核心功能（如任务创建、进度更新）需在3步内完成，确保用户聚焦于业务目标。2.可视化驱动：通过甘特图、燃尽图等动态图表直观展示项目里程碑、资源分配及，支持拖拽调整时间线。3.一致性规范：o遵循公司统一的UI组件库（如Ant Design），保持按钮、表单等元素的交互逻辑与现有系统一致-色彩体系以蓝色（主品牌色）象征协作，橙色标注高优先级任务，灰色区分已完成状态。4.场景化适配：针对敏捷开发与瀑布式项目管理模式，提供可切换模板（如看板/列表视图），默认布局根据用户角色自动优化（如开发人员侧重任务分解，项目经理全局监控）。5.2界面流程图以下为用户操作的核心对应界面跳转逻辑（以创建项目为例）：步骤1：入口触发•路径A：用户从工作台点击“新建项目”按钮→弹出项目基础信息表单（必填字段名称、负责人、起止时间）。•路径B：通过快捷命令（Ctrl+P）输入项目名称→系统自动匹配历史模板，用户可选择复用或新建。步骤2：•表单提交后进入任务编排页，支持两种模式：o手动添加：逐项输入任务名称、分配成员、依赖关系（前置任务可关联下拉选择）。o****：从知识库加载WBS模板（如“APP迭代开发”），自动生成带默认工时的任务树。步骤3：流程生效•点击“发布”后进入项目总览页，系统：-生成唯一项目ID，同步至关联系统（如GitLab创建代码仓库）。o触发通知：邮件告知参与成员，企业IM推送任务卡片（含直接跳转链接）。****：•若必填信息缺失，保存时高亮错误字段并悬浮提示规则（如“截止日期需晚于当前时间”）。•冲突检测：当任务时间重叠时，流程图自动标红冲突节点，方案（如延长周期或增加资源）。（注：完整流程图见附件AX-05，含13个主要状态节点及7条异常分支路径。）6.技术需求6.1技术架构本项目管理模块采用前后端分离的微服务架构，技术栈选型如下：1.前端框架：基于Vue 3.0的Element Plus，配合TypeScript实现强类型校验，确保代码可维护性；2.后端服务：使用Spring Boot 2.7（Java 17）构建RESTful集成Spring Security OAuth2实现权限控制；3.中间件：o消息队列：RabbitMQ 3.11处理异步任务（如甘自动生成）；o缓存层：Redis 7.0集群存储高频访问数据（如项目里程碑状态）；4.部署平台：Kubernetes 1.25（AWS）实现容器化编排，配合Istio服务网格管理流量；5.监控体系：Prometheus+Grafana 9.4实现全链路性能监控，ELK Stack管理日志。架构设计遵循ISO 25010质量标准，支持横向扩展至每秒5000+并发请求，服务可用性≥99.99%。6.2数据管理1.****：o主数据库：PostgreSQL 15分布式集群部署，采用TimescaleDB扩展处理时间序列数据（如项目进度日志）；o文档MongoDB 6.0分片集群存储非结构化数据（如需求附件）；2.数据处理：o实时计算：通过Flink 1.17处理流式数据（如任务变更事件）；o离线分析：Airflow 2.6调度Spark 3.3批处理作业生成项目健康度报表；3.数据库设计：o核心：erDiagram PROJECTS||--o{TASKS:\"1:N\"T}|--||MILESTONES:\"N:1\"PROJECTS}|--||TEAMS:\"N:M\"o索引对project_id、task_deadline等字段建立B+Tree索引，查询性能提升40%；4.数据安全：o传输层：1.3加密所有API通信；o存储层：采用AES-256字段级加密敏感数据（如合同金额）；o备份策略：每日增量备份（RPO≤15分钟至S3 Glacier Deep Archive。7.测试计划7.1测试类型针对项目管理模块的测试工作，需覆盖以下测试类型以确保系统功能完整性、性能稳定性及用户体验达标：1.功能测试o功能验证：包括项目创建、任务分配、进度跟踪、里程碑设置等基础功能的业务逻辑测试。o边界场景测试：如极端时间范围输入、多角色并发操作、异常数据中断等场景错性验证。o权限测试：验证不同角色（如项目经理、成员、访客）的权限控制是否符合需求设计。2.性能测试o负载测试：模拟高（如500+用户同时操作）下的系统响应时间及资源占用率。o压力测试：持续施压至系统崩溃临界点，评估最大承载能力及自动恢复机制。o数据库：针对大规模数据（如10万级任务记录）的查询、聚合操作效率优化。3.兼容性测试o多端适配：涵盖Web端（Chrome/Firefoxari/Edge）、移动端（iOS/Android主流版本）的UI一致性及功能兼容性。o分辨率测试：验证从1366×768至4K屏幕的布局自适应能力。安全测试•OWASP Top 10漏洞扫描：包括SQL注入、XSS攻击、CSRF防护等安全风险点检测。•敏感数据加密：检查、权限令牌等数据的传输与存储加密策略（如AES-256、TLS 1.2+）。5.用户体验测试o交互流程测试：通过A/B测试评估关键路径任务创建流程）的操作效率与用户满意度。o无障碍访问：遵循WCAG 2.1标准，验证屏幕阅读器兼容性及键盘导航支持。7.2测试标准结果的验收需满足以下量化指标与定性要求，确保交付质量符合预期：1.功能测试通过标准o关键路径用例通过率100%，非关键路径通过率≥95%。-所有P0级缺陷（如数据丢失、核心功能失效）必须修复且复测通过，P1级缺陷解决率≥98%。2.性能测试通过标准o响应时间：普通操作2秒，复杂报表生成≤8秒（硬件配置：4核CPU/16GB内存）。o系统稳定性：72小时持续运行下内存泄漏≤3%，CPU平均占用率≤70%。3.测试通过标准o主流浏览器及移动端覆盖率100%，次要浏览器（如IE11）允许非功能性UI差异但需保证基础流程可用。4.安全测试通过标准-所有高危漏洞（CVSS评分≥7.0）必须修复，中低危漏洞修复率≥90%且剩余漏洞需提供风险缓释方案。5.版本发布标准o需通过全测试，且获得QA负责人、产品经理、技术负责人的三方签字确认。o自动化测试覆盖率≥80%（含接口测试与核心业务流UI测试）。注：未达标项需申请，经变更控制委员会（CCB）审批后方可放行。8.项目计划8.1时间表本项目的时间安排分为需求分析、设计开发、测试验证和上线部署四个阶段，具体时间规划如下：1.需求分析阶段（__年__月__日-__月__日）o完成需求调研与业务逻辑梳理，输出需求规格说明书（SRS）。o关键里程碑：需求评审会议通过。2.设计开发__年__月__日-__年__月__日）o技术方案设计与评审，完成系统架构设计文档（TDD）。o模块开发、联调与内部验收，输出自测报告。o关键里程碑：代码冻结。3.测试验证阶段（__年__月__日-__年__月__日）o执行功能测试、性能安全测试，修复所有P0/P1级缺陷。o输出测试报告并通过测试评审。4.上线部署阶段（__年__月__日-__年__月__日）完成生产环境部署、数据迁移及灰度发布。o关键里程碑：系统正式投产并签署上线报告。注：上述时间节点需根据实际资源情况动态调整，并同步更新甘特图。8.2人员安排项目团队分为产品组、开发组、测试组和运维组，具体分工如下：|角色|人数职责|所属阶段||-|||-||产品经理|_|需求分析、原型设计及验收|全周期|后端开发工程师|_|核心模块开发与接口联调|设计开发阶段||前端开发工程师|_实现与交互优化|设计开发阶段||测试工程师|_|用例编写、执行及缺陷跟踪|验证阶段||运维工程师|_|环境搭建与发布支持|上线部署阶段|协作机制：**•每日站会同步进度，每周迭代会议对齐风险点。•开发与测试采用“1:1.5”人力配比，确保测试充分性。•关键节点需全员参与评审决策权归属项目经理。";

        String B1 = "程）的操作效率与用户满意度。o无障碍访问：遵循WCAG 2.1标准，验证屏幕阅读器兼容性及键盘导航支持。7测试标准结果的验收需满足以下量化指标与定性要求，确保交付质量符合预期：功能测试通过标准o关键路径用例通过率100%，非关键路径通过率≥95%。-所有P0级缺陷（如数据丢失、核心功能失效）必须修复且复测通过，P1级缺陷解决率≥98%。2.性能测试通过标准o响应时间：普通操作2秒，复杂报表生成≤8秒（硬件配置：4核CPU/16GB内存）。o系统稳定性：72小时持续运行下内存泄漏≤3%，CPU平均占用率≤70%。3.测试通过";
        String s = extractMostSimilar(A1, B1);
        System.out.println(s); // 输出：天气很好，适合出去（原B末尾是"游玩"，提取结果末尾对齐）
        System.err.println("匹配内容长度:" + B1.length());
        System.err.println("匹配出来的原文长度:" + s.length());

    }

    public static String extractMostSimilar(String A, String B) {
        if (B.isEmpty() || A.length() < B.length()) {
            return "";
        }

        int lenA = A.length();
        int lenB = B.length();
        int maxPossibleLength = Math.min(lenA, lenB);
        int minDistance = Integer.MAX_VALUE;
        String result = "";
        int resultStart = -1;
        int resultEnd = -1;

        // 从最长可能的子串长度开始遍历
        for (int currentLength = maxPossibleLength; currentLength >= 1; currentLength--) {
            // 遍历所有可能的起始位置
            for (int start = 0; start <= lenA - currentLength; start++) {
                int end = start + currentLength - 1; // 当前子串的结束位置
                String sub = A.substring(start, start + currentLength);
                int distance = editDistance(sub, B);

                // 找到更小的编辑距离
                if (distance < minDistance) {
                    minDistance = distance;
                    result = sub;
                    resultStart = start;
                    resultEnd = end;
                    // 完全匹配时直接返回
                    if (distance == 0) {
                        return result;
                    }
                } else if (distance == minDistance) {
                    // 编辑距离相同，优先选择结束位置更接近B末尾的子串
                    int currentEndDiff = Math.abs(end - (lenB - 1));
                    int resultEndDiff = Math.abs(resultEnd - (lenB - 1));
                    if (currentEndDiff < resultEndDiff ||
                            (currentEndDiff == resultEndDiff && start < resultStart)) {
                        result = sub;
                        resultStart = start;
                        resultEnd = end;
                    }
                }
            }
            // 提前终止：已找到完全匹配
            if (minDistance == 0) {
                break;
            }
        }

        return result;
    }

    // 计算Levenshtein编辑距离（优化空间复杂度）
    private static int editDistance(String s1, String s2) {
        // 使用滚动数组优化空间至O(n)
        int[] prevRow = new int[s2.length() + 1];
        int[] currRow = new int[s2.length() + 1];

        // 初始化第一行
        for (int j = 0; j <= s2.length(); j++) {
            prevRow[j] = j;
        }

        for (int i = 1; i <= s1.length(); i++) {
            currRow[0] = i;
            char c1 = s1.charAt(i - 1);
            for (int j = 1; j <= s2.length(); j++) {
                char c2 = s2.charAt(j - 1);
                if (c1 == c2) {
                    currRow[j] = prevRow[j - 1];
                } else {
                    currRow[j] = 1 + Math.min(
                            Math.min(prevRow[j],    // 删除
                                    currRow[j - 1]), // 插入
                            prevRow[j - 1]         // 替换
                    );
                }
            }
            // 滚动数组复用
            int[] temp = prevRow;
            prevRow = currRow;
            currRow = temp;
        }

        return prevRow[s2.length()];
    }
}