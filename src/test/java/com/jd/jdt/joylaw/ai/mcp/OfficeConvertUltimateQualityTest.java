package com.jd.jdt.joylaw.ai.mcp;

import com.jd.jdt.joylaw.ai.mcp.enums.AsposeConvertTypeEnum;
import com.jd.jdt.joylaw.ai.mcp.pojo.dto.AsposeConvertDTO;
import com.jd.jdt.joylaw.ai.mcp.pojo.vo.AsposeConvertVO;
import com.jd.jdt.joylaw.ai.mcp.service.AsposeConvertService;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 终极质量Word转PDF测试类
 * 验证深度优化后的转换效果
 */
@Log4j2
@SpringBootTest
public class OfficeConvertUltimateQualityTest {

    @Autowired
    private AsposeConvertService asposeConvertService;

    @Test
    public void testUltimateQualityWordToPdfConversion() throws IOException {
        log.info("=== 开始终极质量Word转PDF转换测试 ===");
        
        // 1. 读取测试Word文件
        String inputPath = "src/main/resources/static/中信银行股份有限公司智能AB实验系统软件产品及实施服务采购项目合同0711.docx";
        File inputFile = new File(inputPath);
        
        if (!inputFile.exists()) {
            log.warn("测试文件不存在: {}", inputPath);
            return;
        }
        
        byte[] fileBytes = Files.readAllBytes(inputFile.toPath());
        log.info("✓ 读取Word文件成功");
        log.info("  - 文件路径: {}", inputPath);
        log.info("  - 文件大小: {} bytes ({} KB)", fileBytes.length, fileBytes.length / 1024);

        // 2. 构造转换请求
        AsposeConvertDTO dto = new AsposeConvertDTO();
        dto.setFileName(inputFile.getName());
        dto.setByteArray(fileBytes);
        dto.setAsposeConvertType(AsposeConvertTypeEnum.DOCX_TO_PDF);

        // 3. 执行终极质量转换
        log.info("开始执行终极质量Word转PDF转换...");
        long startTime = System.currentTimeMillis();
        
        AsposeConvertVO vo = asposeConvertService.executeDocxToPdfConversion(dto);
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        log.info("✓ 终极质量转换完成！");
        log.info("=== 转换统计信息 ===");
        log.info("  - 转换耗时: {} ms ({:.2f} 秒)", duration, duration / 1000.0);
        log.info("  - 原文件大小: {} bytes ({} KB)", fileBytes.length, fileBytes.length / 1024);
        log.info("  - PDF文件大小: {} bytes ({} KB)", vo.getByteArray().length, vo.getByteArray().length / 1024);
        log.info("  - 文件大小比率: {:.2f}%", (double) vo.getByteArray().length / fileBytes.length * 100);
        log.info("  - 转换速度: {:.2f} KB/s", (fileBytes.length / 1024.0) / (duration / 1000.0));

        // 4. 保存转换结果
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String outputPath = "target/test-output/ultimate_quality_" + timestamp + ".pdf";
        
        // 确保输出目录存在
        File outputFile = new File(outputPath);
        outputFile.getParentFile().mkdirs();
        
        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
            fos.write(vo.getByteArray());
            log.info("✓ 终极质量PDF文件已保存到: {}", outputPath);
        }

        // 5. 验证转换结果
        performDetailedValidation(vo, outputFile);
        
        log.info("=== 终极质量转换测试完成 ===");
        log.info("请检查输出文件: {} 以验证格式保持效果", outputPath);
        printUltimateQualityCheckList();
    }

    @Test
    public void testConversionStabilityAndConsistency() throws IOException {
        log.info("=== 开始转换稳定性和一致性测试 ===");
        
        String inputPath = "src/main/resources/static/【样例】技术服务协议模板（jd为销售方通用版）.docx";
        File inputFile = new File(inputPath);
        
        if (!inputFile.exists()) {
            log.warn("测试文件不存在: {}", inputPath);
            return;
        }
        
        byte[] fileBytes = Files.readAllBytes(inputFile.toPath());
        
        // 执行多次转换测试一致性
        int testRounds = 3;
        long[] durations = new long[testRounds];
        int[] fileSizes = new int[testRounds];
        
        for (int i = 1; i <= testRounds; i++) {
            log.info("执行第 {} 轮一致性测试", i);
            
            AsposeConvertDTO dto = new AsposeConvertDTO();
            dto.setFileName(inputFile.getName());
            dto.setByteArray(fileBytes);
            dto.setAsposeConvertType(AsposeConvertTypeEnum.DOCX_TO_PDF);
            
            long startTime = System.currentTimeMillis();
            AsposeConvertVO vo = asposeConvertService.executeDocxToPdfConversion(dto);
            long endTime = System.currentTimeMillis();
            
            durations[i-1] = endTime - startTime;
            fileSizes[i-1] = vo.getByteArray().length;
            
            log.info("  - 第 {} 轮: 耗时 {} ms, 大小 {} bytes", i, durations[i-1], fileSizes[i-1]);
            
            // 保存每轮结果用于对比
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String outputPath = "target/test-output/consistency_round_" + i + "_" + timestamp + ".pdf";
            
            File outputFile = new File(outputPath);
            outputFile.getParentFile().mkdirs();
            
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                fos.write(vo.getByteArray());
            }
        }
        
        // 分析一致性
        analyzeConsistency(durations, fileSizes);
        
        log.info("=== 稳定性和一致性测试完成 ===");
    }

    @Test
    public void testMemoryUsageAndPerformance() throws IOException {
        log.info("=== 开始内存使用和性能测试 ===");
        
        String inputPath = "src/main/resources/static/【样例】技术服务协议模板（jd为销售方通用版）.docx";
        File inputFile = new File(inputPath);
        
        if (!inputFile.exists()) {
            log.warn("测试文件不存在: {}", inputPath);
            return;
        }
        
        byte[] fileBytes = Files.readAllBytes(inputFile.toPath());
        
        // 获取运行时信息
        Runtime runtime = Runtime.getRuntime();
        
        // 转换前内存状态
        runtime.gc(); // 建议垃圾回收
        long memoryBefore = runtime.totalMemory() - runtime.freeMemory();
        
        log.info("转换前内存使用: {} MB", memoryBefore / (1024 * 1024));
        
        // 执行转换
        AsposeConvertDTO dto = new AsposeConvertDTO();
        dto.setFileName(inputFile.getName());
        dto.setByteArray(fileBytes);
        dto.setAsposeConvertType(AsposeConvertTypeEnum.DOCX_TO_PDF);
        
        long startTime = System.currentTimeMillis();
        AsposeConvertVO vo = asposeConvertService.executeDocxToPdfConversion(dto);
        long endTime = System.currentTimeMillis();
        
        // 转换后内存状态
        long memoryAfter = runtime.totalMemory() - runtime.freeMemory();
        long memoryUsed = memoryAfter - memoryBefore;
        
        log.info("=== 性能分析结果 ===");
        log.info("  - 转换耗时: {} ms", endTime - startTime);
        log.info("  - 转换后内存使用: {} MB", memoryAfter / (1024 * 1024));
        log.info("  - 内存增量: {} MB", memoryUsed / (1024 * 1024));
        log.info("  - 内存效率: {:.2f} bytes/KB", (double) memoryUsed / (fileBytes.length / 1024.0));
        
        // 保存结果
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String outputPath = "target/test-output/performance_test_" + timestamp + ".pdf";
        
        File outputFile = new File(outputPath);
        outputFile.getParentFile().mkdirs();
        
        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
            fos.write(vo.getByteArray());
        }
        
        log.info("=== 内存使用和性能测试完成 ===");
    }
    
    /**
     * 执行详细验证
     */
    private void performDetailedValidation(AsposeConvertVO vo, File outputFile) {
        log.info("=== 执行详细验证 ===");
        
        // 1. 基本验证
        assert vo.getByteArray().length > 0 : "转换后的PDF文件不能为空";
        assert vo.getFileName().endsWith(".pdf") : "输出文件名应该以.pdf结尾";
        assert vo.getByteArray().length > 1000 : "PDF文件大小应该合理";
        
        // 2. PDF格式验证
        byte[] pdfBytes = vo.getByteArray();
        if (pdfBytes.length >= 4) {
            String header = new String(pdfBytes, 0, 4);
            assert "%PDF".equals(header) : "PDF格式头部验证失败";
            log.info("✓ PDF格式验证通过");
        }
        
        // 3. 文件大小合理性验证
        long fileSize = outputFile.length();
        assert fileSize > 10000 : "PDF文件大小过小，可能转换不完整";
        assert fileSize < 50 * 1024 * 1024 : "PDF文件大小过大，可能存在问题";
        log.info("✓ 文件大小验证通过: {} bytes", fileSize);
        
        // 4. 文件可读性验证
        assert outputFile.exists() : "输出文件不存在";
        assert outputFile.canRead() : "输出文件不可读";
        log.info("✓ 文件可读性验证通过");
        
        log.info("=== 详细验证完成 ===");
    }
    
    /**
     * 分析一致性
     */
    private void analyzeConsistency(long[] durations, int[] fileSizes) {
        log.info("=== 一致性分析结果 ===");
        
        // 计算平均值
        long avgDuration = 0;
        int avgFileSize = 0;
        for (int i = 0; i < durations.length; i++) {
            avgDuration += durations[i];
            avgFileSize += fileSizes[i];
        }
        avgDuration /= durations.length;
        avgFileSize /= fileSizes.length;
        
        log.info("  - 平均转换时间: {} ms", avgDuration);
        log.info("  - 平均文件大小: {} bytes", avgFileSize);
        
        // 计算变异系数
        double durationVariance = 0;
        double sizeVariance = 0;
        for (int i = 0; i < durations.length; i++) {
            durationVariance += Math.pow(durations[i] - avgDuration, 2);
            sizeVariance += Math.pow(fileSizes[i] - avgFileSize, 2);
        }
        durationVariance /= durations.length;
        sizeVariance /= fileSizes.length;
        
        double durationStdDev = Math.sqrt(durationVariance);
        double sizeStdDev = Math.sqrt(sizeVariance);
        
        log.info("  - 转换时间标准差: {:.2f} ms", durationStdDev);
        log.info("  - 文件大小标准差: {:.2f} bytes", sizeStdDev);
        
        // 一致性评估
        double durationCV = durationStdDev / avgDuration * 100;
        double sizeCV = sizeStdDev / avgFileSize * 100;
        
        log.info("  - 转换时间变异系数: {:.2f}%", durationCV);
        log.info("  - 文件大小变异系数: {:.2f}%", sizeCV);
        
        // 一致性判断
        if (durationCV < 10 && sizeCV < 1) {
            log.info("✓ 转换一致性优秀");
        } else if (durationCV < 20 && sizeCV < 5) {
            log.info("✓ 转换一致性良好");
        } else {
            log.warn("⚠ 转换一致性需要改进");
        }
    }
    
    /**
     * 打印终极质量检查清单
     */
    private void printUltimateQualityCheckList() {
        log.info("");
        log.info("=== 终极质量PDF检查清单 ===");
        log.info("请手动验证以下格式保持情况（深度优化版）:");
        log.info("");
        log.info("【字体质量检查】");
        log.info("□ 1. 中文字体完美显示（宋体、黑体、微软雅黑、楷体、仿宋）");
        log.info("□ 2. 英文字体完美显示（Times New Roman、Arial、Calibri、Cambria）");
        log.info("□ 3. 字体大小精确保持，无缩放变形");
        log.info("□ 4. 字体粗细（加粗、正常）完全一致");
        log.info("□ 5. 字体样式（斜体、下划线）完全保持");
        log.info("");
        log.info("【格式质量检查】");
        log.info("□ 6. 文字颜色和背景色精确还原");
        log.info("□ 7. 段落间距和行距完全一致");
        log.info("□ 8. 文本对齐方式（左对齐、居中、右对齐、两端对齐）保持");
        log.info("□ 9. 首行缩进和段落缩进精确保持");
        log.info("□ 10. 项目符号和编号格式完整");
        log.info("");
        log.info("【表格质量检查】");
        log.info("□ 11. 表格边框线条粗细和颜色正确");
        log.info("□ 12. 单元格背景色和文字颜色保持");
        log.info("□ 13. 表格列宽和行高精确保持");
        log.info("□ 14. 单元格内文字对齐方式正确");
        log.info("□ 15. 合并单元格格式完整");
        log.info("");
        log.info("【页面布局检查】");
        log.info("□ 16. 页面边距完全一致");
        log.info("□ 17. 页眉页脚内容和格式完整");
        log.info("□ 18. 页码位置和格式正确");
        log.info("□ 19. 分页位置准确，无异常分页");
        log.info("□ 20. 页面方向（横向/纵向）保持");
        log.info("");
        log.info("【图像质量检查】");
        log.info("□ 21. 图片清晰度高，无模糊");
        log.info("□ 22. 图片位置和大小精确");
        log.info("□ 23. 图片环绕文字方式保持");
        log.info("□ 24. 图片颜色和对比度正确");
        log.info("");
        log.info("【功能性检查】");
        log.info("□ 25. 超链接可点击且跳转正确");
        log.info("□ 26. 书签和目录结构完整");
        log.info("□ 27. 交叉引用功能正常");
        log.info("□ 28. 脚注和尾注格式正确");
        log.info("");
        log.info("【整体质量检查】");
        log.info("□ 29. 整体视觉效果与Word文档100%一致");
        log.info("□ 30. 无任何格式丢失或变形");
        log.info("□ 31. 文档结构完整，无缺失内容");
        log.info("□ 32. PDF文件大小合理，质量优秀");
        log.info("");
        log.info("如果以上所有项目都符合预期，说明终极质量格式保持修复成功！");
        log.info("=== 检查清单结束 ===");
    }
}