package com.jd.jdt.joylaw.ai.mcp.rpc.feign;

import com.jd.jdt.joylaw.ai.mcp.enums.AsposeConvertEnum;
import com.jd.jdt.joylaw.ai.mcp.pojo.ResponseResult;
import com.jd.jdt.joylaw.ai.mcp.pojo.dto.AsposeConvertDTO;
import com.jd.jdt.joylaw.ai.mcp.pojo.dto.AsposeConvertChunkedDTO;
import com.jd.jdt.joylaw.ai.mcp.pojo.vo.AsposeConvertVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@SpringBootTest
public class OfficeConvertFeignClientImprovedTest {

    @Autowired
    private AsposeConvertFeignService officeConvertFeignClient;

    private static final String TEST_FILE_PATH = "src/main/resources/static/【样例】技术服务协议模板（jd为销售方通用版）.docx";
    private static final String OUTPUT_DIR = "src/main/resources/static/";

    @Test
    public void testOfficeConvertWithDiagnostics() throws IOException {
        log.info("=== 开始HTTP 413错误诊断测试 ===");
        
        // 准备测试文件
        File inputFile = new File(TEST_FILE_PATH);
        assertTrue(inputFile.exists(), "测试文件不存在");
        byte[] fileContent = Files.readAllBytes(inputFile.toPath());
        
        // 诊断日志：文件大小信息
        long fileSizeBytes = fileContent.length;
        double fileSizeMB = fileSizeBytes / (1024.0 * 1024.0);
        log.info("原始文件大小: {} bytes ({} MB)", fileSizeBytes, String.format("%.2f", fileSizeMB));
        
        // 测试原始DTO的payload大小
        AsposeConvertDTO originalDTO = new AsposeConvertDTO();
        originalDTO.setFileName(inputFile.getName());
        originalDTO.setByteArray(fileContent);
        originalDTO.setOfficeConvert(AsposeConvertEnum.DOCX_TO_PDF);
        
        // 估算JSON序列化后的大小（Base64编码会增加约33%）
        long estimatedJsonSize = (long) (fileSizeBytes * 1.33) + 200; // 额外200字节用于JSON结构
        double estimatedJsonSizeMB = estimatedJsonSize / (1024.0 * 1024.0);
        log.info("预估JSON payload大小: {} bytes ({} MB)", estimatedJsonSize, String.format("%.2f", estimatedJsonSizeMB));
        
        // 测试压缩DTO
        AsposeConvertChunkedDTO compressedDTO = AsposeConvertChunkedDTO.fromByteArray(
            inputFile.getName(), fileContent, AsposeConvertEnum.DOCX_TO_PDF);
        
        log.info("压缩后大小: {} bytes ({} MB)", compressedDTO.getCompressedSize(), 
            String.format("%.2f", compressedDTO.getCompressedSize() / (1024.0 * 1024.0)));
        log.info("压缩比率: {}", String.format("%.2f%%", compressedDTO.getCompressionRatio() * 100));
        
        // 如果压缩后仍然很大，记录警告
        if (compressedDTO.getCompressedSize() > 10 * 1024 * 1024) { // 10MB
            log.warn("警告：即使压缩后，payload仍然超过10MB，可能仍会遇到413错误");
        }
        
        try {
            log.info("尝试调用远程服务...");
            
            // 首先尝试使用原始DTO（预期会失败）
            log.info("测试1：使用原始DTO（预期413错误）");
            try {
                ResponseResult<AsposeConvertVO> response = officeConvertFeignClient.officeConvert(originalDTO);
                log.info("意外成功：原始DTO调用成功，响应码: {}", response.getCode());
            } catch (Exception e) {
                log.error("预期的错误：原始DTO调用失败: {}", e.getMessage());
                if (e.getMessage().contains("413") || e.getMessage().contains("Payload Too Large")) {
                    log.info("确认：遇到413 Payload Too Large错误，诊断正确");
                }
            }
            
            // 注意：由于服务器可能不支持压缩DTO格式，这里只是演示
            // 实际使用时需要服务器端也支持解压缩
            log.info("测试2：压缩DTO已准备就绪，但需要服务器端支持解压缩");
            log.info("压缩DTO信息 - 原始大小: {}MB, 压缩后: {}MB, 压缩比: {}%", 
                String.format("%.2f", compressedDTO.getOriginalSize() / (1024.0 * 1024.0)),
                String.format("%.2f", compressedDTO.getCompressedSize() / (1024.0 * 1024.0)),
                String.format("%.1f", compressedDTO.getCompressionRatio() * 100));
            
        } catch (Exception e) {
            log.error("调用失败: {}", e.getMessage(), e);
            
            // 分析错误类型
            if (e.getMessage().contains("413")) {
                log.error("确认诊断：遇到HTTP 413 Payload Too Large错误");
                log.error("建议解决方案：");
                log.error("1. 增加服务器端请求体大小限制");
                log.error("2. 实现数据压缩（当前压缩比: {}%)", 
                    String.format("%.1f", compressedDTO.getCompressionRatio() * 100));
                log.error("3. 考虑分块传输或文件上传接口");
            } else if (e.getMessage().contains("timeout")) {
                log.error("遇到超时错误，可能是网络或服务器处理时间问题");
            } else {
                log.error("遇到其他错误: {}", e.getMessage());
            }
            
            // 重新抛出异常以便测试框架处理
            throw e;
        }
        
        log.info("=== 诊断测试完成 ===");
    }

    @Test
    public void testFileSizeAnalysis() throws IOException {
        log.info("=== 文件大小分析测试 ===");
        
        File inputFile = new File(TEST_FILE_PATH);
        if (!inputFile.exists()) {
            log.warn("测试文件不存在，跳过分析");
            return;
        }
        
        byte[] fileContent = Files.readAllBytes(inputFile.toPath());
        
        // 分析不同大小限制下的情况
        long[] sizeLimits = {1024 * 1024, 5 * 1024 * 1024, 10 * 1024 * 1024, 50 * 1024 * 1024}; // 1MB, 5MB, 10MB, 50MB
        String[] sizeNames = {"1MB", "5MB", "10MB", "50MB"};
        
        for (int i = 0; i < sizeLimits.length; i++) {
            long limit = sizeLimits[i];
            String name = sizeNames[i];
            
            long estimatedPayload = (long) (fileContent.length * 1.33) + 200;
            boolean wouldFit = estimatedPayload <= limit;
            
            log.info("服务器限制 {}: 文件是否适合 = {}", name, wouldFit ? "是" : "否");
        }
        
        // 测试压缩效果
        AsposeConvertChunkedDTO compressed = AsposeConvertChunkedDTO.fromByteArray(
            inputFile.getName(), fileContent, AsposeConvertEnum.DOCX_TO_PDF);
        
        log.info("压缩效果分析:");
        log.info("- 原始大小: {} bytes", compressed.getOriginalSize());
        log.info("- 压缩后大小: {} bytes", compressed.getCompressedSize());
        log.info("- 节省空间: {} bytes", compressed.getOriginalSize() - compressed.getCompressedSize());
        log.info("- 压缩比率: {}%", String.format("%.1f", compressed.getCompressionRatio() * 100));
        
        log.info("=== 文件大小分析完成 ===");
    }
}