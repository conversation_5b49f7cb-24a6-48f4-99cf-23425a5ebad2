package com.jd.jdt.joylaw.ai.mcp;

import com.jd.jdt.joylaw.ai.mcp.enums.AsposeConvertEnum;
import com.jd.jdt.joylaw.ai.mcp.pojo.ResponseResult;
import com.jd.jdt.joylaw.ai.mcp.pojo.dto.AsposeConvertCompressedDTO;
import com.jd.jdt.joylaw.ai.mcp.pojo.dto.AsposeConvertDTO;
import com.jd.jdt.joylaw.ai.mcp.pojo.vo.AsposeConvertVO;
import com.jd.jdt.joylaw.ai.mcp.service.impl.AsposeConvertEnhancedServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Random;

/**
 * <p>
 * HTTP 413错误解决方案测试
 * 测试压缩传输、数据大小监控、传输策略等功能
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@SpringBootTest
public class Http413SolutionTest {

    @Autowired(required = false)
    private AsposeConvertEnhancedServiceImpl enhancedService;

    /**
     * 测试压缩DTO的基本功能
     */
    @Test
    public void testCompressedDTOBasicFunctionality() {
        log.info("=== 测试压缩DTO基本功能 ===");

        // 创建测试数据
        AsposeConvertDTO originalDTO = createTestDTO("test-document.docx", 1024 * 1024); // 1MB
        
        // 测试压缩
        AsposeConvertCompressedDTO compressedDTO = AsposeConvertCompressedDTO.fromOriginal(originalDTO);
        
        log.info("压缩结果: {}", compressedDTO.getCompressionInfo());
        
        // 验证压缩效果
        assert compressedDTO.getCompressedSize() < compressedDTO.getOriginalSize();
        assert compressedDTO.getCompressionRatio() < 1.0;
        
        // 测试解压
        AsposeConvertDTO decompressedDTO = compressedDTO.toOriginal();
        
        // 验证解压结果
        assert decompressedDTO.getFileName().equals(originalDTO.getFileName());
        assert decompressedDTO.getOfficeConvert().equals(originalDTO.getOfficeConvert());
        assert decompressedDTO.getByteArray().length == originalDTO.getByteArray().length;
        
        log.info("压缩DTO基本功能测试通过");
    }

    /**
     * 测试不同文件大小的压缩效果
     */
    @Test
    public void testCompressionEffectivenessForDifferentSizes() {
        log.info("=== 测试不同文件大小的压缩效果 ===");

        int[] testSizes = {
            1024,           // 1KB
            10 * 1024,      // 10KB
            100 * 1024,     // 100KB
            1024 * 1024,    // 1MB
            5 * 1024 * 1024, // 5MB
            10 * 1024 * 1024 // 10MB
        };

        for (int size : testSizes) {
            AsposeConvertDTO originalDTO = createTestDTO("test-" + size + ".docx", size);
            AsposeConvertCompressedDTO compressedDTO = AsposeConvertCompressedDTO.fromOriginal(originalDTO);
            
            log.info("文件大小: {} bytes, 压缩效果: {}", size, compressedDTO.getCompressionInfo());
            
            // 验证压缩有效性
            assert compressedDTO.getCompressedSize() <= compressedDTO.getOriginalSize();
        }
        
        log.info("不同文件大小压缩效果测试完成");
    }

    /**
     * 测试传输策略选择
     */
    @Test
    public void testTransmissionStrategySelection() {
        log.info("=== 测试传输策略选择 ===");

        if (enhancedService == null) {
            log.warn("增强服务未注入，跳过传输策略测试");
            return;
        }

        long[] testSizes = {
            500 * 1024,      // 500KB
            2 * 1024 * 1024, // 2MB
            15 * 1024 * 1024, // 15MB
            60 * 1024 * 1024  // 60MB
        };

        for (long size : testSizes) {
            String strategy = enhancedService.getTransmissionStrategy(size);
            log.info("文件大小: {} bytes ({}), 推荐策略: {}", 
                    size, formatFileSize(size), strategy);
        }
        
        log.info("传输策略选择测试完成");
    }

    /**
     * 测试大文件处理能力
     */
    @Test
    public void testLargeFileHandling() {
        log.info("=== 测试大文件处理能力 ===");

        // 创建较大的测试文件 (20MB)
        AsposeConvertDTO largeDTO = createTestDTO("large-document.docx", 20 * 1024 * 1024);
        
        log.info("创建大文件测试数据: {} bytes", largeDTO.getByteArray().length);
        
        // 测试压缩
        long startTime = System.currentTimeMillis();
        AsposeConvertCompressedDTO compressedDTO = AsposeConvertCompressedDTO.fromOriginal(largeDTO);
        long compressionTime = System.currentTimeMillis() - startTime;
        
        log.info("大文件压缩完成 - 耗时: {} ms, 压缩效果: {}", 
                compressionTime, compressedDTO.getCompressionInfo());
        
        // 测试解压
        startTime = System.currentTimeMillis();
        AsposeConvertDTO decompressedDTO = compressedDTO.toOriginal();
        long decompressionTime = System.currentTimeMillis() - startTime;
        
        log.info("大文件解压完成 - 耗时: {} ms", decompressionTime);
        
        // 验证数据完整性
        assert decompressedDTO.getByteArray().length == largeDTO.getByteArray().length;
        
        log.info("大文件处理能力测试通过");
    }

    /**
     * 测试错误处理
     */
    @Test
    public void testErrorHandling() {
        log.info("=== 测试错误处理 ===");

        // 测试空数据处理
        try {
            AsposeConvertDTO nullDTO = new AsposeConvertDTO();
            nullDTO.setFileName("null-test.docx");
            nullDTO.setByteArray(null);
            nullDTO.setOfficeConvert(AsposeConvertEnum.WORD_TO_HTML);
            
            AsposeConvertCompressedDTO.fromOriginal(nullDTO);
            assert false : "应该抛出异常";
        } catch (IllegalArgumentException e) {
            log.info("空数据错误处理正确: {}", e.getMessage());
        }

        // 测试空压缩数据解压
        try {
            AsposeConvertCompressedDTO emptyCompressed = new AsposeConvertCompressedDTO();
            emptyCompressed.setFileName("empty-test.docx");
            emptyCompressed.setCompressedData(null);
            emptyCompressed.setOfficeConvert(AsposeConvertEnum.WORD_TO_HTML);
            
            emptyCompressed.toOriginal();
            assert false : "应该抛出异常";
        } catch (IllegalStateException e) {
            log.info("空压缩数据错误处理正确: {}", e.getMessage());
        }
        
        log.info("错误处理测试通过");
    }

    /**
     * 性能基准测试
     */
    @Test
    public void testPerformanceBenchmark() {
        log.info("=== 性能基准测试 ===");

        int[] testSizes = {1024 * 1024, 5 * 1024 * 1024, 10 * 1024 * 1024}; // 1MB, 5MB, 10MB
        int iterations = 3;

        for (int size : testSizes) {
            log.info("测试文件大小: {} ({})", size, formatFileSize(size));
            
            long totalCompressionTime = 0;
            long totalDecompressionTime = 0;
            double totalCompressionRatio = 0;

            for (int i = 0; i < iterations; i++) {
                AsposeConvertDTO originalDTO = createTestDTO("benchmark-" + size + "-" + i + ".docx", size);
                
                // 压缩性能测试
                long startTime = System.currentTimeMillis();
                AsposeConvertCompressedDTO compressedDTO = AsposeConvertCompressedDTO.fromOriginal(originalDTO);
                long compressionTime = System.currentTimeMillis() - startTime;
                totalCompressionTime += compressionTime;
                totalCompressionRatio += compressedDTO.getCompressionRatio();
                
                // 解压性能测试
                startTime = System.currentTimeMillis();
                compressedDTO.toOriginal();
                long decompressionTime = System.currentTimeMillis() - startTime;
                totalDecompressionTime += decompressionTime;
            }

            log.info("平均压缩时间: {} ms, 平均解压时间: {} ms, 平均压缩比: {:.2f}%",
                    totalCompressionTime / iterations,
                    totalDecompressionTime / iterations,
                    (totalCompressionRatio / iterations) * 100);
        }
        
        log.info("性能基准测试完成");
    }

    /**
     * 创建测试用的AsposeConvertDTO
     */
    private AsposeConvertDTO createTestDTO(String fileName, int size) {
        AsposeConvertDTO dto = new AsposeConvertDTO();
        dto.setFileName(fileName);
        dto.setOfficeConvert(AsposeConvertEnum.WORD_TO_HTML);
        dto.setByteArray(generateTestData(size));
        return dto;
    }

    /**
     * 生成测试数据
     */
    private byte[] generateTestData(int size) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Random random = new Random();
        
        // 生成模拟文档内容
        String baseContent = "这是一个测试文档内容，用于验证HTTP 413错误解决方案。";
        byte[] baseBytes = baseContent.getBytes(StandardCharsets.UTF_8);
        
        while (baos.size() < size) {
            try {
                baos.write(baseBytes);
                // 添加一些随机数据以增加真实性
                baos.write(random.nextInt(256));
            } catch (IOException e) {
                throw new RuntimeException("生成测试数据失败", e);
            }
        }
        
        // 截取到指定大小
        byte[] result = baos.toByteArray();
        if (result.length > size) {
            byte[] trimmed = new byte[size];
            System.arraycopy(result, 0, trimmed, 0, size);
            return trimmed;
        }
        
        return result;
    }

    /**
     * 格式化文件大小显示
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }
}