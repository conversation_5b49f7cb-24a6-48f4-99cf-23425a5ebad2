package com.jd.jdt.joylaw.ai.mcp;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

/**
 * 简单的MCP流式测试客户端 - 使用纯Java HTTP连接
 * 不依赖Spring WebFlux等库，便于直接运行
 */
public class SimpleMcpTest {

    private static final String BASE_URL = "http://localhost:8081";
    private static final String MCP_ENDPOINT = "/mcp/message";

    public static void main(String[] args) {
        System.out.println("开始测试MCP流式功能...");
        
        // 测试简单流式工具
        testSimpleMcpStreamTool();
        
        // 测试完整流式工具
        testFullMcpStreamTool();
    }
    
    /**
     * 测试简单的MCP流式工具
     */
    private static void testSimpleMcpStreamTool() {
        System.out.println("\n===== 测试简单MCP流式工具 =====");
        
        String requestBody = "{\n" +
                "  \"tool\": \"simpleMcpStreamTool\",\n" +
                "  \"arguments\": {\n" +
                "    \"message\": \"这是一个流式测试消息！\"\n" +
                "  }\n" +
                "}";
        
        sendRequest(requestBody);
    }
    
    /**
     * 测试完整的MCP流式工具
     */
    private static void testFullMcpStreamTool() {
        System.out.println("\n===== 测试完整MCP流式工具 =====");
        
        String requestBody = "{\n" +
                "  \"tool\": \"testMcpStreamTool\",\n" +
                "  \"arguments\": {\n" +
                "    \"testMessage\": \"这是一个完整的流式测试！\",\n" +
                "    \"chunkCount\": \"5\",\n" +
                "    \"delayMs\": \"300\"\n" +
                "  }\n" +
                "}";
        
        sendRequest(requestBody);
    }
    
    /**
     * 发送HTTP请求并处理流式响应
     */
    private static void sendRequest(String requestBody) {
        try {
            URL url = new URL(BASE_URL + MCP_ENDPOINT);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "application/json");
            connection.setDoOutput(true);
            
            // 发送请求体
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            
            // 读取响应
            int responseCode = connection.getResponseCode();
            System.out.println("HTTP响应码: " + responseCode);
            
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        System.out.println("收到数据: " + line);
                    }
                }
            } else {
                System.err.println("请求失败，响应码: " + responseCode);
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        System.err.println(line);
                    }
                }
            }
            
            connection.disconnect();
            
        } catch (Exception e) {
            System.err.println("请求异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}