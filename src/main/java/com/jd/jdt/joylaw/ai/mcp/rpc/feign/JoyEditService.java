package com.jd.jdt.joylaw.ai.mcp.rpc.feign;

import com.jd.jdt.joylaw.ai.mcp.config.FeignConfig;
import com.jd.jdt.joylaw.ai.mcp.pojo.dto.DoronChatDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import org.springframework.web.bind.annotation.RequestHeader;


/**
 * <p>
 * JoyEdit服务调用
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@FeignClient(name = "joy-eidt-service", url = "${rpc.joyeidt.url}", configuration = FeignConfig.class)
public interface JoyEditService {

    @PostMapping(value = "/api/v1/llm/chat/doron", consumes = "application/json")
    String chatDoronStream(@RequestHeader("user_name") String userName, @RequestBody DoronChatDTO doronChatDTO);

}
