package com.jd.jdt.joylaw.ai.mcp.controller;

import com.jd.jdt.joylaw.ai.mcp.service.AsposeConvertRule;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PostMapping;

import java.io.IOException;

import com.jd.jdt.joylaw.ai.mcp.pojo.dto.AsposeConvertDTO;
import com.jd.jdt.joylaw.ai.mcp.pojo.vo.AsposeConvertVO;
import com.jd.jdt.joylaw.ai.mcp.pojo.ResponseResult;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>
 * AsposeConvertControlle 相关接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Log4j2
@RestController
@RequestMapping("/api/v1/aspose-convert")
public class AsposeConvertControlle {

    @Autowired
    private AsposeConvertRule asposeConvertRule;

    /**
     * word文档格式互转
     *
     * @param asposeConvertDTO
     * @return
     * @throws IOException
     */
    @PostMapping(value = "/word")
    public ResponseResult<AsposeConvertVO> officeConvert(@RequestBody AsposeConvertDTO asposeConvertDTO) throws IOException {
        return ResponseResult.success(asposeConvertRule.executeConvertRule(asposeConvertDTO));
    }

}
