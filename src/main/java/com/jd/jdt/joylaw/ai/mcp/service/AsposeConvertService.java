package com.jd.jdt.joylaw.ai.mcp.service;

import com.jd.jdt.joylaw.ai.mcp.pojo.dto.AsposeConvertDTO;
import com.jd.jdt.joylaw.ai.mcp.pojo.vo.AsposeConvertVO;

/**
 * <p>
 * 文件格式转换服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
public interface AsposeConvertService {

    

    /**
     * DOCX转PDF转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeDocxToPdfConversion(AsposeConvertDTO asposeConvertDTO);

    /**
     * DOCX转HTML转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeDocxToHtmlConversion(AsposeConvertDTO asposeConvertDTO);

    /**
     * HTML转DOCX转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeHtmlToDocxConversion(AsposeConvertDTO asposeConvertDTO);

    // 文本格式转换
    /**
     * DOCX转TXT转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeDocxToTxtConversion(AsposeConvertDTO asposeConvertDTO);

    /**
     * DOCX转RTF转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeDocxToRtfConversion(AsposeConvertDTO asposeConvertDTO);

    // 其他文档格式转换
    /**
     * DOCX转ODT转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeDocxToOdtConversion(AsposeConvertDTO asposeConvertDTO);

    /**
     * DOCX转EPUB转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeDocxToEpubConversion(AsposeConvertDTO asposeConvertDTO);

    /**
     * DOCX转XPS转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeDocxToXpsConversion(AsposeConvertDTO asposeConvertDTO);

    /**
     * DOCX转MHTML转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeDocxToMhtmlConversion(AsposeConvertDTO asposeConvertDTO);

    // 图片格式转换
    /**
     * DOCX转JPEG转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeDocxToJpegConversion(AsposeConvertDTO asposeConvertDTO);

    /**
     * DOCX转PNG转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeDocxToPngConversion(AsposeConvertDTO asposeConvertDTO);

    /**
     * DOCX转TIFF转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeDocxToTiffConversion(AsposeConvertDTO asposeConvertDTO);

    /**
     * DOCX转BMP转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeDocxToBmpConversion(AsposeConvertDTO asposeConvertDTO);

    /**
     * DOCX转SVG转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeDocxToSvgConversion(AsposeConvertDTO asposeConvertDTO);

    // 表格格式转换
    /**
     * DOCX转XLSX转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeDocxToXlsxConversion(AsposeConvertDTO asposeConvertDTO);

    // 其他格式转换
    /**
     * DOCX转Markdown转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeDocxToMarkdownConversion(AsposeConvertDTO asposeConvertDTO);

    // 反向转换
    /**
     * RTF转DOCX转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeRtfToDocxConversion(AsposeConvertDTO asposeConvertDTO);

    /**
     * TXT转DOCX转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeTxtToDocxConversion(AsposeConvertDTO asposeConvertDTO);

    /**
     * ODT转DOCX转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeOdtToDocxConversion(AsposeConvertDTO asposeConvertDTO);

    /**
     * DOC转DOCX转换
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    AsposeConvertVO executeDocToDocxConversion(AsposeConvertDTO asposeConvertDTO);

}