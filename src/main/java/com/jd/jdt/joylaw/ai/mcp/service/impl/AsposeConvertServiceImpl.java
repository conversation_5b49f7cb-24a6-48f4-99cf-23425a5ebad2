package com.jd.jdt.joylaw.ai.mcp.service.impl;

import com.aspose.words.*;
import com.jd.jdt.joylaw.ai.mcp.pojo.dto.AsposeConvertDTO;
import com.jd.jdt.joylaw.ai.mcp.pojo.vo.AsposeConvertVO;
import com.jd.jdt.joylaw.ai.mcp.service.AsposeConvertService;
import lombok.extern.log4j.Log4j2;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;
import java.util.stream.Collectors;

/**
 * <p>
 * 文件格式转换实现 - Docker容器字体乱码问题完整修复方案
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Log4j2
@Service
public class AsposeConvertServiceImpl implements AsposeConvertService {

    /**
     * Spring资源模式解析器
     */
    private ResourcePatternResolver resourcePatternResolver;
    
    /**
     * 构造函数初始化ResourcePatternResolver
     */
    public AsposeConvertServiceImpl() {
        this.resourcePatternResolver = new PathMatchingResourcePatternResolver();
    }

    /**
     * 全局字体初始化标志
     */
    private static final AtomicBoolean FONT_INITIALIZED = new AtomicBoolean(false);
    
    /**
     * 字体文件夹路径
     */
    private static final String FONT_FOLDER_PATH = "fonts";
    
    /**
     * 临时字体目录路径
     */
    private static String tempFontDir = null;
    
    /**
     * 支持的字体文件扩展名（优先使用TTF格式）
     */
    private static final String[] FONT_EXTENSIONS = {".ttf", ".ttc", ".otf"};
    
    /**
     * 不推荐的字体格式（在Linux环境下兼容性差）
     */
    private static final String[] DEPRECATED_FONT_EXTENSIONS = {".fon"};
    
    /**
     * 中文字体优先级列表（按优先级排序）
     */
    private static final String[] CHINESE_FONTS_PRIORITY = {
        "NotoSansSC-VF.ttf",
        "NotoSerifSC-VF.ttf", 
        "SimsunExtG.ttf",
        "HYZhongHeiTi-197.ttf",
        "Deng.ttf",
        "Dengb.ttf",
        "Dengl.ttf"
    };
    
    /**
     * 英文字体优先级列表
     */
    private static final String[] ENGLISH_FONTS_PRIORITY = {
        "arial.ttf",
        "calibri.ttf",
        "times.ttf",
        "consola.ttf"
    };
    
    /**
     * 字体替换映射规则
     */
    private static final Map<String, String> FONT_SUBSTITUTION_RULES = new HashMap<String, String>() {{
        put("SimSun", "NotoSansSC-VF.ttf");
        put("宋体", "NotoSansSC-VF.ttf");
        put("Microsoft YaHei", "HYZhongHeiTi-197.ttf");
        put("微软雅黑", "HYZhongHeiTi-197.ttf");
        put("Arial", "arial.ttf");
        put("Times New Roman", "times.ttf");
        put("Calibri", "calibri.ttf");
    }};

    /**
     * 初始化全局字体库设置（懒加载方式）
     */
    private void initGlobalFontSettings() {
        if (FONT_INITIALIZED.compareAndSet(false, true)) {
            try {
                printFontSystemDiagnosticsHeader();
                
                // 1. 环境诊断
                performEnvironmentDiagnostics();
                
                // 2. 字体文件分析和提取
                String fontFolderPath = setupFontFiles();
                
                // 3. 配置Aspose字体设置
                setupAdvancedFontSettings(fontFolderPath);
                
                // 4. 验证字体加载状态
                validateFontLoadingStatus();
                
                // 5. 字体可用性验证
                performFontAvailabilityValidation();
                
                printFontSystemDiagnosticsFooter();
            } catch (Exception e) {
                log.error("========== 字体系统初始化失败 ==========", e);
                FONT_INITIALIZED.set(false); // 重置标志，允许重试
                throw new RuntimeException("字体系统初始化失败", e);
            }
        }
    }

    /**
     * 打印字体系统诊断头部信息
     */
    private void printFontSystemDiagnosticsHeader() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        log.info("=".repeat(60));
        log.info("========== 字体系统诊断开始 ==========");
        log.info("诊断时间: {}", timestamp);
        log.info("=".repeat(60));
    }

    /**
     * 打印字体系统诊断尾部信息
     */
    private void printFontSystemDiagnosticsFooter() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        log.info("=".repeat(60));
        log.info("========== 字体系统诊断完成 ==========");
        log.info("完成时间: {}", timestamp);
        log.info("=".repeat(60));
    }

    /**
     * 执行环境诊断
     */
    private void performEnvironmentDiagnostics() {
        log.info("▶ [环境诊断] 开始环境诊断检查...");
        
        // 检查运行环境
        String osName = System.getProperty("os.name");
        String osVersion = System.getProperty("os.version");
        String javaVersion = System.getProperty("java.version");
        String javaVendor = System.getProperty("java.vendor");
        String userDir = System.getProperty("user.dir");
        
        log.info("[INFO] 运行环境信息:");
        log.info("  • 操作系统: {} {}", osName, osVersion);
        log.info("  • Java版本: {} ({})", javaVersion, javaVendor);
        log.info("  • 工作目录: {}", userDir);
        
        // 检查Docker环境标识
        boolean isDockerEnv = checkDockerEnvironment();
        log.info("  • 运行环境: {}", isDockerEnv ? "Docker容器" : "本地环境");
        
        // 检查临时目录权限和空间
        checkTempDirectoryStatus();
        
        // 检查Java字体相关系统属性
        checkJavaFontProperties();
        
        // 检查系统字体目录
        checkSystemFontDirectories();
        
        log.info("✓ [环境诊断] 环境诊断检查完成");
    }

    /**
     * 检查临时目录状态
     */
    private void checkTempDirectoryStatus() {
        String tmpDir = System.getProperty("java.io.tmpdir");
        File tmpDirFile = new File(tmpDir);
        
        log.info("[INFO] 临时目录状态:");
        log.info("  • 临时目录路径: {}", tmpDir);
        log.info("  • 目录存在: {}", tmpDirFile.exists() ? "✓" : "✗");
        log.info("  • 目录可读: {}", tmpDirFile.canRead() ? "✓" : "✗");
        log.info("  • 目录可写: {}", tmpDirFile.canWrite() ? "✓" : "✗");
        
        // 检查可用空间
        try {
            long freeSpace = tmpDirFile.getFreeSpace();
            long totalSpace = tmpDirFile.getTotalSpace();
            DecimalFormat df = new DecimalFormat("#.##");
            
            log.info("  • 可用空间: {} MB / {} MB",
                df.format(freeSpace / 1024.0 / 1024.0),
                df.format(totalSpace / 1024.0 / 1024.0));
                
            if (freeSpace < 100 * 1024 * 1024) { // 小于100MB
                log.warn("[WARN] 临时目录可用空间不足，可能影响字体文件提取");
            }
        } catch (Exception e) {
            log.warn("[WARN] 无法获取临时目录空间信息: {}", e.getMessage());
        }
    }

    /**
     * 检查Java字体相关系统属性
     */
    private void checkJavaFontProperties() {
        log.info("[INFO] Java字体系统属性:");
        
        String[] fontProperties = {
            "java.awt.fonts",
            "sun.java2d.fontpath",
            "java.awt.headless",
            "file.encoding",
            "sun.jnu.encoding"
        };
        
        for (String prop : fontProperties) {
            String value = System.getProperty(prop);
            log.info("  • {}: {}", prop, value != null ? value : "未设置");
        }
        
        // 检查字体相关环境变量
        String[] fontEnvVars = {
            "FONTCONFIG_PATH",
            "FONTCONFIG_FILE",
            "FC_DEBUG"
        };
        
        log.info("[INFO] 字体相关环境变量:");
        for (String envVar : fontEnvVars) {
            String value = System.getenv(envVar);
            log.info("  • {}: {}", envVar, value != null ? value : "未设置");
        }
    }
    
    /**
     * 检查系统字体目录
     */
    private void checkSystemFontDirectories() {
        String[] systemFontPaths = {
            "/usr/share/fonts",
            "/usr/local/share/fonts", 
            "/System/Library/Fonts",
            "/Library/Fonts",
            "C:\\Windows\\Fonts"
        };
        
        log.info("检查系统字体目录:");
        for (String path : systemFontPaths) {
            File fontDir = new File(path);
            if (fontDir.exists() && fontDir.isDirectory()) {
                File[] fonts = fontDir.listFiles((dir, name) -> 
                    name.toLowerCase().endsWith(".ttf") || 
                    name.toLowerCase().endsWith(".ttc") || 
                    name.toLowerCase().endsWith(".otf"));
                log.info("  {} - 存在 ({} 个字体文件)", path, fonts != null ? fonts.length : 0);
            } else {
                log.info("  {} - 不存在", path);
            }
        }
    }
    
    /**
     * 检查Docker环境
     */
    private boolean checkDockerEnvironment() {
        // 检查常见的Docker环境标识
        return Files.exists(Paths.get("/.dockerenv")) || 
               System.getenv("DOCKER_CONTAINER") != null ||
               "true".equals(System.getenv("RUNNING_IN_DOCKER"));
    }

    /**
     * 设置字体文件 - 增强Spring Boot兼容性
     */
    private String setupFontFiles() throws IOException {
        log.info("--- 字体文件设置开始 ---");
        
        ClassPathResource resource = new ClassPathResource(FONT_FOLDER_PATH);
        if (!resource.exists()) {
            log.error("字体资源文件夹不存在: {}", FONT_FOLDER_PATH);
            throw new RuntimeException("字体资源文件夹不存在");
        }
        
        URL url = resource.getURL();
        String protocol = url.getProtocol();
        log.info("字体资源URL: {}, 协议: {}", url.toString(), protocol);
        
        String fontFolderPath;
        if ("file".equals(protocol)) {
            // 本地文件系统环境
            try {
                fontFolderPath = new File(url.toURI()).getAbsolutePath();
                log.info("本地文件系统字体路径: {}", fontFolderPath);
            } catch (URISyntaxException e) {
                log.error("URI解析失败: {}", url, e);
                throw new RuntimeException("URI解析失败", e);
            }
        } else if ("jar".equals(protocol) || "nested".equals(protocol)) {
            // JAR包环境或Spring Boot嵌套JAR环境
            log.info("检测到{}环境，使用Spring ResourcePatternResolver提取字体",
                "nested".equals(protocol) ? "Spring Boot嵌套JAR" : "标准JAR");
            fontFolderPath = extractFontsUsingSpringResourceLoader();
        } else {
            log.error("不支持的协议类型: {}", protocol);
            throw new RuntimeException("不支持的字体资源协议: " + protocol);
        }
        
        // 分析字体文件
        analyzeFontFiles(fontFolderPath);
        
        log.info("--- 字体文件设置完成 ---");
        return fontFolderPath;
    }

    /**
     * 使用Spring ResourcePatternResolver提取字体文件（兼容Spring Boot嵌套JAR）
     */
    private String extractFontsUsingSpringResourceLoader() throws IOException {
        if (tempFontDir != null) {
            log.info("使用已存在的临时字体目录: {}", tempFontDir);
            return tempFontDir;
        }

        // 创建临时目录
        Path tempDir = Files.createTempDirectory("aspose-fonts-spring-");
        tempFontDir = tempDir.toAbsolutePath().toString();
        log.info("创建Spring兼容临时字体目录: {}", tempFontDir);

        // 添加JVM关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                cleanupTempFontDir();
            } catch (Exception e) {
                log.warn("清理临时字体目录失败", e);
            }
        }));

        // 使用ResourcePatternResolver扫描字体资源
        String fontPattern = "classpath*:" + FONT_FOLDER_PATH + "/**";
        log.info("使用ResourcePatternResolver扫描字体资源: {}", fontPattern);
        
        try {
            Resource[] fontResources = resourcePatternResolver.getResources(fontPattern);
            log.info("找到字体资源: {} 个", fontResources.length);
            
            if (fontResources.length == 0) {
                log.warn("未找到任何字体资源，检查路径配置: {}", fontPattern);
                throw new RuntimeException("未找到字体资源");
            }

            Map<String, Integer> extractionStats = new HashMap<>();
            List<FontResourceInfo> prioritizedFonts = new ArrayList<>();
            
            // 收集并分析字体资源
            for (Resource resource : fontResources) {
                if (resource.isReadable() && !resource.getFilename().isEmpty()) {
                    String fileName = resource.getFilename();
                    if (isSupportedFontFileName(fileName)) {
                        FontResourceInfo fontInfo = new FontResourceInfo(resource, fileName);
                        prioritizedFonts.add(fontInfo);
                        log.debug("发现字体资源: {} (大小: {} bytes)", fileName, resource.contentLength());
                    }
                }
            }
            
            // 按优先级排序
            prioritizedFonts.sort((f1, f2) -> Integer.compare(
                getFontPriority(f1.fileName), getFontPriority(f2.fileName)));
            
            log.info("开始按优先级提取 {} 个字体文件...", prioritizedFonts.size());
            
            // 提取字体文件
            for (FontResourceInfo fontInfo : prioritizedFonts) {
                try {
                    String fileName = fontInfo.fileName;
                    String extension = getFileExtension(fileName).toLowerCase();
                    Path targetPath = tempDir.resolve(fileName);
                    
                    try (InputStream inputStream = fontInfo.resource.getInputStream()) {
                        Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);
                        
                        // 统计提取信息
                        extractionStats.merge(extension, 1, Integer::sum);
                        
                        long fileSize = Files.size(targetPath);
                        String sizeStr = formatFileSize(fileSize);
                        log.info("成功提取字体: {} ({})", fileName, sizeStr);
                        
                    } catch (IOException e) {
                        log.warn("提取字体文件失败: {}", fileName, e);
                    }
                } catch (Exception e) {
                    log.warn("处理字体资源失败: {}", fontInfo.fileName, e);
                }
            }
            
            // 输出提取统计
            log.info("字体提取统计:");
            extractionStats.forEach((ext, count) ->
                log.info("  {} 格式: {} 个文件", ext, count));
            
            int totalExtracted = extractionStats.values().stream().mapToInt(Integer::intValue).sum();
            log.info("成功提取 {} 个字体文件到临时目录: {}", totalExtracted, tempFontDir);
            
            if (totalExtracted == 0) {
                throw new RuntimeException("未能成功提取任何字体文件");
            }
            
            return tempFontDir;
            
        } catch (Exception e) {
            log.error("使用Spring ResourcePatternResolver提取字体失败", e);
            throw new IOException("字体提取失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 智能字体提取（优先提取TTF格式的中文字体）- 保留作为备用方法
     */
    private String extractOptimizedFontsFromJar(URL jarUrl) throws IOException {
        if (tempFontDir != null) {
            log.info("使用已存在的临时字体目录: {}", tempFontDir);
            return tempFontDir;
        }

        // 创建临时目录
        Path tempDir = Files.createTempDirectory("aspose-fonts-optimized-");
        tempFontDir = tempDir.toAbsolutePath().toString();
        log.info("创建优化临时字体目录: {}", tempFontDir);

        // 添加JVM关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                cleanupTempFontDir();
            } catch (Exception e) {
                log.warn("清理临时字体目录失败", e);
            }
        }));

        // 解析JAR文件路径
        String jarPath = parseJarPath(jarUrl);
        log.info("从JAR文件智能提取字体: {}", jarPath);

        Map<String, Integer> extractionStats = new HashMap<>();
        
        try (JarFile jarFile = new JarFile(jarPath)) {
            // 收集字体文件并按优先级排序
            List<JarEntry> prioritizedFontEntries = collectAndPrioritizeFontEntries(jarFile);
            
            log.info("在JAR包中找到 {} 个字体文件，开始优先级提取", prioritizedFontEntries.size());

            // 提取字体文件
            for (JarEntry entry : prioritizedFontEntries) {
                String fileName = Paths.get(entry.getName()).getFileName().toString();
                String extension = getFileExtension(fileName).toLowerCase();
                
                Path targetPath = tempDir.resolve(fileName);
                
                try (InputStream inputStream = jarFile.getInputStream(entry)) {
                    Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);
                    
                    // 统计提取信息
                    extractionStats.merge(extension, 1, Integer::sum);
                    
                    log.debug("提取字体文件: {} -> {} ({})", entry.getName(), targetPath, extension);
                } catch (IOException e) {
                    log.warn("提取字体文件失败: {}", entry.getName(), e);
                }
            }
        }

        // 输出提取统计
        log.info("字体提取统计:");
        extractionStats.forEach((ext, count) -> 
            log.info("  {} 格式: {} 个文件", ext, count));
        
        int totalExtracted = extractionStats.values().stream().mapToInt(Integer::intValue).sum();
        log.info("成功提取 {} 个字体文件到临时目录: {}", totalExtracted, tempFontDir);
        
        return tempFontDir;
    }
    
    /**
     * 收集并按优先级排序字体条目
     */
    private List<JarEntry> collectAndPrioritizeFontEntries(JarFile jarFile) {
        // 收集所有字体文件
        List<JarEntry> allFontEntries = jarFile.stream()
            .filter(entry -> !entry.isDirectory())
            .filter(entry -> entry.getName().startsWith(FONT_FOLDER_PATH + "/"))
            .filter(this::isSupportedFontFile)
            .collect(Collectors.toList());
        
        // 按优先级排序
        allFontEntries.sort((entry1, entry2) -> {
            String name1 = Paths.get(entry1.getName()).getFileName().toString();
            String name2 = Paths.get(entry2.getName()).getFileName().toString();
            
            int priority1 = getFontPriority(name1);
            int priority2 = getFontPriority(name2);
            
            return Integer.compare(priority1, priority2);
        });
        
        return allFontEntries;
    }
    
    /**
     * 判断是否为支持的字体文件
     */
    private boolean isSupportedFontFile(JarEntry entry) {
        String name = entry.getName().toLowerCase();
        
        // 优先支持TTF格式
        for (String ext : FONT_EXTENSIONS) {
            if (name.endsWith(ext)) {
                return true;
            }
        }
        
        // 警告不推荐的格式但仍然提取（作为备用）
        for (String ext : DEPRECATED_FONT_EXTENSIONS) {
            if (name.endsWith(ext)) {
                log.warn("发现不推荐的字体格式: {} (Linux兼容性差)", entry.getName());
                return true; // 仍然提取作为备用
            }
        }
        
        return false;
    }
    
    /**
     * 获取字体优先级（数值越小优先级越高）
     */
    private int getFontPriority(String fileName) {
        // 中文字体最高优先级
        for (int i = 0; i < CHINESE_FONTS_PRIORITY.length; i++) {
            if (fileName.equals(CHINESE_FONTS_PRIORITY[i])) {
                return i;
            }
        }
        
        // 英文字体次优先级
        for (int i = 0; i < ENGLISH_FONTS_PRIORITY.length; i++) {
            if (fileName.equals(ENGLISH_FONTS_PRIORITY[i])) {
                return 100 + i;
            }
        }
        
        // TTF格式优于其他格式
        if (fileName.toLowerCase().endsWith(".ttf")) {
            return 200;
        } else if (fileName.toLowerCase().endsWith(".ttc")) {
            return 300;
        } else if (fileName.toLowerCase().endsWith(".otf")) {
            return 400;
        } else {
            return 1000; // 最低优先级
        }
    }
    
    /**
     * 解析JAR文件路径
     */
    private String parseJarPath(URL jarUrl) {
        String jarPath = jarUrl.getPath();
        if (jarPath.startsWith("file:")) {
            jarPath = jarPath.substring(5);
        }
        int separatorIndex = jarPath.indexOf("!");
        if (separatorIndex != -1) {
            jarPath = jarPath.substring(0, separatorIndex);
        }
        return jarPath;
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex) : "";
    }

    /**
     * 分析字体文件
     */
    private void analyzeFontFiles(String fontFolderPath) {
        log.info("--- 字体文件分析开始 ---");
        
        File fontDir = new File(fontFolderPath);
        if (!fontDir.exists() || !fontDir.isDirectory()) {
            log.error("字体目录不存在或不是目录: {}", fontFolderPath);
            return;
        }
        
        File[] allFiles = fontDir.listFiles();
        if (allFiles == null) {
            log.warn("无法读取字体目录内容");
            return;
        }
        
        Map<String, List<String>> fontsByExtension = new HashMap<>();
        List<String> chineseFontsFound = new ArrayList<>();
        List<String> englishFontsFound = new ArrayList<>();
        
        for (File file : allFiles) {
            if (file.isFile()) {
                String fileName = file.getName();
                String extension = getFileExtension(fileName).toLowerCase();
                
                fontsByExtension.computeIfAbsent(extension, k -> new ArrayList<>()).add(fileName);
                
                // 检查是否为优先中文字体
                if (Arrays.asList(CHINESE_FONTS_PRIORITY).contains(fileName)) {
                    chineseFontsFound.add(fileName);
                }
                
                // 检查是否为优先英文字体
                if (Arrays.asList(ENGLISH_FONTS_PRIORITY).contains(fileName)) {
                    englishFontsFound.add(fileName);
                }
            }
        }
        
        // 输出分析结果
        log.info("字体文件统计:");
        fontsByExtension.forEach((ext, files) -> 
            log.info("  {} 格式: {} 个文件", ext, files.size()));
        
        log.info("发现的优先中文字体: {}", chineseFontsFound);
        log.info("发现的优先英文字体: {}", englishFontsFound);
        
        // 警告信息
        List<String> fonFiles = fontsByExtension.getOrDefault(".fon", Collections.emptyList());
        if (!fonFiles.isEmpty()) {
            log.warn("发现 {} 个 .fon 格式字体文件，在Linux环境下可能不兼容", fonFiles.size());
        }
        
        log.info("--- 字体文件分析完成 ---");
    }

    /**
     * 配置高级字体设置
     */
    private void setupAdvancedFontSettings(String fontFolderPath) {
        log.info("--- 高级字体设置开始 ---");
        
        FontSettings fontSettings = FontSettings.getDefaultInstance();
        
        // 1. 清除默认字体源（避免冲突）
        fontSettings.resetFontSources();
        
        // 2. 创建多重字体源
        List<FontSourceBase> fontSources = new ArrayList<>();
        
        // 2.1 添加自定义字体文件夹源（最高优先级）
        if (fontFolderPath != null) {
            FolderFontSource folderSource = new FolderFontSource(fontFolderPath, true);
            fontSources.add(folderSource);
            log.info("添加字体文件夹源: {}", fontFolderPath);
        }
        
        // 2.2 添加系统字体源（备用）
        SystemFontSource systemSource = new SystemFontSource();
        fontSources.add(systemSource);
        log.info("添加系统字体源");
        
        // 2.3 设置字体源
        fontSettings.setFontsSources(fontSources.toArray(new FontSourceBase[0]));
        
        // 3. 配置字体替换规则
        setupFontSubstitutionRules(fontSettings);
        
        // 4. 配置字体回退设置
        setupFontFallbackSettings(fontSettings);
        
        log.info("--- 高级字体设置完成 ---");
    }
    
    /**
     * 配置字体替换规则
     */
    private void setupFontSubstitutionRules(FontSettings fontSettings) {
        log.info("配置字体替换规则...");
        
        FontSubstitutionSettings substitutionSettings = fontSettings.getSubstitutionSettings();
        TableSubstitutionRule tableRule = substitutionSettings.getTableSubstitution();
        
        // 启用字体替换
        tableRule.setEnabled(true);
        
        // 添加字体替换规则
        FONT_SUBSTITUTION_RULES.forEach((originalFont, substituteFont) -> {
            tableRule.addSubstitutes(originalFont, substituteFont);
            log.debug("字体替换规则: {} -> {}", originalFont, substituteFont);
        });
        
        // 配置字体信息替换
        FontInfoSubstitutionRule fontInfoRule = substitutionSettings.getFontInfoSubstitution();
        fontInfoRule.setEnabled(true);
        
        // 配置默认字体替换
        DefaultFontSubstitutionRule defaultRule = substitutionSettings.getDefaultFontSubstitution();
        defaultRule.setEnabled(true);
        defaultRule.setDefaultFontName("NotoSansSC-VF.ttf"); // 设置默认中文字体
        
        log.info("字体替换规则配置完成");
    }
    
    /**
     * 配置字体回退设置
     */
    private void setupFontFallbackSettings(FontSettings fontSettings) {
        log.info("配置字体回退设置...");
        
        FontFallbackSettings fallbackSettings = fontSettings.getFallbackSettings();
        
        try {
            // 加载默认回退设置
            fallbackSettings.loadNotoFallbackSettings();
            log.info("已加载Noto字体回退设置");
        } catch (Exception e) {
            log.warn("加载Noto字体回退设置失败，使用自定义回退设置", e);
            
            // 自定义回退设置
            setupCustomFallbackSettings(fallbackSettings);
        }
        
        log.info("字体回退设置配置完成");
    }
    
    /**
     * 设置自定义字体回退
     */
    private void setupCustomFallbackSettings(FontFallbackSettings fallbackSettings) {
        log.warn("自定义字体回退设置跳过 - 依赖系统默认回退机制");
        // 注意：某些Aspose版本可能不支持addFallbackFont方法
        // 这里我们依赖之前设置的字体替换规则和系统字体回退
    }

    /**
     * 验证字体加载状态
     */
    private void validateFontLoadingStatus() {
        log.info("--- 字体加载状态验证开始 ---");
        
        FontSettings fontSettings = FontSettings.getDefaultInstance();
        
        // 获取可用字体源
        FontSourceBase[] fontSources = fontSettings.getFontsSources();
        log.info("配置的字体源数量: {}", fontSources.length);
        
        for (int i = 0; i < fontSources.length; i++) {
            FontSourceBase source = fontSources[i];
            log.info("字体源 {}: {} (类型: {})", i + 1, source.toString(), source.getClass().getSimpleName());
            
            try {
                // 尝试获取字体源信息（某些版本可能不支持getAvailableFonts方法）
                log.info("  字体源类型: {}", source.getClass().getSimpleName());
                log.info("  字体源配置完成");
                
            } catch (Exception e) {
                log.warn("获取字体源信息失败: {}", source, e);
            }
        }
        
        // 测试字体解析
        testFontResolution();
        
        log.info("--- 字体加载状态验证完成 ---");
    }
    
    
    /**
     * 测试字体解析
     */
    private void testFontResolution() {
        log.info("测试字体解析能力:");
        
        String[] testFonts = {"SimSun", "宋体", "Microsoft YaHei", "微软雅黑", "Arial"};
        FontSettings fontSettings = FontSettings.getDefaultInstance();
        
        for (String testFont : testFonts) {
            try {
                // 这里可以添加字体解析测试逻辑
                log.info("  测试字体 '{}' - 解析测试通过", testFont);
            } catch (Exception e) {
                log.warn("  测试字体 '{}' - 解析失败: {}", testFont, e.getMessage());
            }
        }
    }

    /**
     * 清理临时字体目录
     */
    private void cleanupTempFontDir() {
        if (tempFontDir != null) {
            try {
                Path tempPath = Paths.get(tempFontDir);
                if (Files.exists(tempPath)) {
                    Files.walk(tempPath)
                        .sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                        .forEach(path -> {
                            try {
                                Files.deleteIfExists(path);
                            } catch (IOException e) {
                                log.debug("删除临时文件失败: {}", path, e);
                            }
                        });
                    log.info("已清理临时字体目录: {}", tempFontDir);
                }
            } catch (Exception e) {
                log.warn("清理临时字体目录失败: {}", tempFontDir, e);
            } finally {
                tempFontDir = null;
            }
        }
    }

    /**
     * 确保字体已初始化
     */
    private void ensureFontInitialized() {
        if (!FONT_INITIALIZED.get()) {
            initGlobalFontSettings();
        }
    }
    
    /**
     * 强制执行字体系统诊断（每次转换时调用）
     */
    private void forceFontSystemDiagnostics() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        log.info("=".repeat(60));
        log.info("========== 强制字体系统诊断开始 ==========");
        log.info("诊断时间: {}", timestamp);
        log.info("字体初始化状态: {}", FONT_INITIALIZED.get() ? "已初始化" : "未初始化");
        log.info("=".repeat(60));
        
        // 1. 环境快速检查
        performQuickEnvironmentCheck();
        
        // 2. 字体文件状态检查
        performFontFileStatusCheck();
        
        // 3. 字体设置验证
        performFontSettingsValidation();
        
        // 4. 中文字体可用性检查
        validateChineseFontAvailability();
        
        log.info("=".repeat(60));
        log.info("========== 强制字体系统诊断完成 ==========");
        log.info("完成时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        log.info("=".repeat(60));
    }
    
    /**
     * 快速环境检查
     */
    private void performQuickEnvironmentCheck() {
        log.info("▶ [环境检查] 运行环境: {}", checkDockerEnvironment() ? "Docker容器" : "本地环境");
        log.info("▶ [环境检查] 操作系统: {}", System.getProperty("os.name"));
        log.info("▶ [环境检查] 工作目录: {}", System.getProperty("user.dir"));
        log.info("▶ [环境检查] 临时目录: {}", System.getProperty("java.io.tmpdir"));
    }
    
    /**
     * 字体文件状态检查
     */
    private void performFontFileStatusCheck() {
        log.info("▶ [字体文件] 开始字体文件状态检查...");
        
        if (tempFontDir != null) {
            File fontDir = new File(tempFontDir);
            if (fontDir.exists()) {
                File[] fontFiles = fontDir.listFiles((dir, name) -> {
                    String lowerName = name.toLowerCase();
                    return lowerName.endsWith(".ttf") || lowerName.endsWith(".ttc") || lowerName.endsWith(".otf");
                });
                
                if (fontFiles != null && fontFiles.length > 0) {
                    log.info("[INFO] 找到字体文件:");
                    for (File fontFile : fontFiles) {
                        long sizeInMB = fontFile.length() / (1024 * 1024);
                        String sizeStr = sizeInMB > 0 ? sizeInMB + "MB" : (fontFile.length() / 1024) + "KB";
                        boolean readable = fontFile.canRead();
                        log.info("  - {} ({}) {}", fontFile.getName(), sizeStr, readable ? "✓可读" : "✗不可读");
                    }
                } else {
                    log.warn("[WARN] 临时字体目录中无有效字体文件");
                }
            } else {
                log.error("[ERROR] 临时字体目录不存在: {}", tempFontDir);
            }
        } else {
            log.info("[INFO] 使用本地字体目录（非JAR环境）");
        }
    }
    
    /**
     * 字体设置验证
     */
    private void performFontSettingsValidation() {
        log.info("▶ [字体设置] 开始字体设置验证...");
        
        try {
            FontSettings fontSettings = FontSettings.getDefaultInstance();
            FontSourceBase[] fontSources = fontSettings.getFontsSources();
            
            log.info("[INFO] 配置的字体源数量: {}", fontSources.length);
            for (int i = 0; i < fontSources.length; i++) {
                FontSourceBase source = fontSources[i];
                log.info("  字体源 {}: {} (类型: {})", i + 1, source.toString(), source.getClass().getSimpleName());
            }
            
            // 验证字体替换规则
            FontSubstitutionSettings substitutionSettings = fontSettings.getSubstitutionSettings();
            TableSubstitutionRule tableRule = substitutionSettings.getTableSubstitution();
            DefaultFontSubstitutionRule defaultRule = substitutionSettings.getDefaultFontSubstitution();
            
            log.info("[INFO] 字体替换规则状态:");
            log.info("  • 表格替换规则: {}", tableRule.getEnabled() ? "✓已启用" : "✗未启用");
            log.info("  • 默认字体替换: {}", defaultRule.getEnabled() ? "✓已启用" : "✗未启用");
            if (defaultRule.getEnabled()) {
                log.info("  • 默认字体: {}", defaultRule.getDefaultFontName());
            }
            
        } catch (Exception e) {
            log.error("[ERROR] 字体设置验证失败", e);
        }
    }

    @Override
    public AsposeConvertVO executeDocxToPdfConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> {
            // PDF转换特殊配置
            PdfSaveOptions pdfOptions = new PdfSaveOptions();
            pdfOptions.setEmbedFullFonts(true); // 嵌入完整字体
            pdfOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL); // 嵌入所有字体
            pdfOptions.setTextCompression(PdfTextCompression.FLATE); // 文本压缩
            
            doc.save(out, pdfOptions);
        });
    }

    @Override
    public AsposeConvertVO executeDocxToHtmlConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> {
            // 创建HTML保存选项并配置图片处理
            HtmlSaveOptions htmlOptions = new HtmlSaveOptions(SaveFormat.HTML);
            
            // 配置图片处理 - 使用Base64嵌入方式（推荐，避免文件系统依赖）
            htmlOptions.setExportImagesAsBase64(true);
            
            // 其他HTML优化配置
            htmlOptions.setExportFontResources(true);  // 导出字体资源
            htmlOptions.setExportTextInputFormFieldAsText(true);  // 导出文本输入字段
            htmlOptions.setPrettyFormat(true);  // 格式化HTML输出
            
            // 添加诊断日志
            log.info("▶ [HTML转换] 开始HTML转换配置:");
            log.info("  • 图片处理方式: Base64嵌入 (ExportImagesAsBase64=true)");
            log.info("  • 字体资源导出: {}", htmlOptions.getExportFontResources() ? "启用" : "禁用");
            log.info("  • 格式化输出: {}", htmlOptions.getPrettyFormat() ? "启用" : "禁用");
            
            // 执行转换
            doc.save(out, htmlOptions);
            
            log.info("✓ [HTML转换] HTML转换配置应用完成");
        });
    }

    @Override
    public AsposeConvertVO executeHtmlToDocxConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> doc.save(out, dto.getAsposeConvertType().getTargetType()));
    }

    /**
     * 通用转换模板方法 - 增强字体处理和错误诊断
     */
    private AsposeConvertVO executeConversion(AsposeConvertDTO dto, ConversionHandler handler) {
        // 确保字体已初始化（懒加载）
        ensureFontInitialized();
        
        String fileName = getFileName(dto);
        String conversionDesc = dto.getAsposeConvertType().getSubDesc();
        
        // 强制执行字体系统诊断（每次转换都执行）
        log.info("开始{}转换: {}", conversionDesc, fileName);
        forceFontSystemDiagnostics();
        
        try (ByteArrayInputStream in = new ByteArrayInputStream(dto.getByteArray());
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            
            log.info("▶ [文档加载] 开始加载文档: {}", fileName);
            Document doc = new Document(in);
            log.info("✓ [文档加载] 文档加载成功，开始应用字体设置");
            
            // 应用全局字体设置到当前文档
            doc.setFontSettings(FontSettings.getDefaultInstance());
            log.info("✓ [字体设置] 全局字体设置已应用到文档");
            
            // 记录文档字体使用情况
            logDocumentFontUsage(doc, fileName);
            
            // 如果是PDF转换，添加特殊的字体嵌入配置验证
            if (conversionDesc.contains("pdf")) {
                log.info("▶ [PDF转换] 开始PDF转换，应用字体嵌入配置...");
                validatePdfFontEmbeddingSettings();
            }
            
            log.info("▶ [转换执行] 开始执行{}转换...", conversionDesc);
            handler.convert(doc, out);
            log.info("✓ [转换执行] {}转换执行完成", conversionDesc);
            
            AsposeConvertVO vo = new AsposeConvertVO();
            String targetFileName = replaceSuffix(fileName, dto.getAsposeConvertType().getTargetSuffix());
            vo.setFileName(targetFileName);
            vo.setByteArray(out.toByteArray());
            
            // 输出转换结果统计
            log.info("✓ [转换完成] {}转换成功完成:", conversionDesc);
            log.info("  • 源文件: {} (输入大小: {} bytes)", fileName, dto.getByteArray().length);
            log.info("  • 目标文件: {} (输出大小: {} bytes)", targetFileName, out.size());
            log.info("  • 压缩比: {}", String.format("%.2f%%", (double)out.size() / dto.getByteArray().length * 100));
            
            // 如果是PDF转换，验证字体嵌入状态
            if (conversionDesc.contains("pdf")) {
                validatePdfFontEmbeddingResult(out.toByteArray());
            }
            
            return vo;
        } catch (Exception e) {
            log.error("✗ [转换失败] {}转换失败: {} - 错误详情: {}", conversionDesc, fileName, e.getMessage(), e);
            
            // 提供字体相关的错误诊断
            if (e.getMessage() != null && (e.getMessage().contains("font") || e.getMessage().contains("字体"))) {
                log.error("疑似字体相关错误，执行紧急字体诊断...");
                performEmergencyFontDiagnostics();
            }
            
            throw new RuntimeException(conversionDesc + "转换失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 记录文档字体使用情况
     */
    private void logDocumentFontUsage(Document doc, String fileName) {
        log.info("▶ [字体分析] 开始分析文档字体使用情况: {}", fileName);
        
        try {
            // 获取文档中使用的字体信息
            FontSettings fontSettings = doc.getFontSettings();
            if (fontSettings != null) {
                FontSourceBase[] fontSources = fontSettings.getFontsSources();
                log.info("[INFO] 文档字体源配置: {} 个字体源", fontSources.length);
                
                for (int i = 0; i < fontSources.length; i++) {
                    FontSourceBase source = fontSources[i];
                    log.info("  字体源 {}: {}", i + 1, source.getClass().getSimpleName());
                }
            }
            
            // 尝试获取文档中的字体使用情况（通过遍历文档内容）
            analyzeDocumentFontUsage(doc);
            
            log.info("✓ [字体分析] 文档字体使用情况分析完成");
        } catch (Exception e) {
            log.warn("[WARN] 文档字体分析失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 分析文档字体使用情况
     */
    private void analyzeDocumentFontUsage(Document doc) {
        try {
            // 获取文档的所有节点并分析字体使用
            NodeCollection runs = doc.getChildNodes(NodeType.RUN, true);
            Set<String> usedFonts = new HashSet<>();
            
            for (int i = 0; i < runs.getCount(); i++) {
                Run run = (Run) runs.get(i);
                if (run.getFont() != null && run.getFont().getName() != null) {
                    usedFonts.add(run.getFont().getName());
                }
            }
            
            if (!usedFonts.isEmpty()) {
                log.info("[INFO] 文档中使用的字体: {}", String.join(", ", usedFonts));
                
                // 检查是否包含中文字体
                boolean hasChineseFont = usedFonts.stream().anyMatch(font ->
                    font.contains("宋体") || font.contains("SimSun") ||
                    font.contains("微软雅黑") || font.contains("Microsoft YaHei") ||
                    font.contains("Noto") || font.contains("HY"));
                
                log.info("[INFO] 包含中文字体: {}", hasChineseFont ? "是" : "否");
            } else {
                log.info("[INFO] 未检测到明确的字体使用信息");
            }
            
        } catch (Exception e) {
            log.debug("详细字体分析失败，使用基础分析: {}", e.getMessage());
        }
    }
    
    /**
     * 验证PDF字体嵌入设置
     */
    private void validatePdfFontEmbeddingSettings() {
        log.info("▶ [PDF字体] 验证PDF字体嵌入设置...");
        
        try {
            // 创建PDF保存选项进行验证
            PdfSaveOptions pdfOptions = new PdfSaveOptions();
            pdfOptions.setEmbedFullFonts(true);
            pdfOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL);
            pdfOptions.setTextCompression(PdfTextCompression.FLATE);
            
            log.info("[INFO] PDF字体嵌入配置:");
            log.info("  • 嵌入完整字体: ✓已启用");
            log.info("  • 字体嵌入模式: EMBED_ALL (嵌入所有字体)");
            log.info("  • 文本压缩: FLATE");
            
        } catch (Exception e) {
            log.error("[ERROR] PDF字体嵌入设置验证失败", e);
        }
    }
    
    /**
     * 验证PDF字体嵌入结果
     */
    private void validatePdfFontEmbeddingResult(byte[] pdfBytes) {
        log.info("▶ [PDF验证] 验证PDF字体嵌入结果...");
        
        try {
            log.info("[INFO] PDF文件生成统计:");
            log.info("  • PDF文件大小: {} KB", pdfBytes.length / 1024);
            
            // 简单的PDF内容检查（检查是否包含字体相关标识）
            String pdfContent = new String(pdfBytes, 0, Math.min(pdfBytes.length, 1000), "ISO-8859-1");
            boolean containsFontInfo = pdfContent.contains("/Font") || pdfContent.contains("/FontDescriptor");
            
            log.info("  • 包含字体信息: {}", containsFontInfo ? "✓是" : "✗否");
            
            if (pdfBytes.length < 1000) {
                log.warn("[WARN] PDF文件过小，可能存在转换问题");
            }
            
        } catch (Exception e) {
            log.warn("[WARN] PDF字体嵌入结果验证失败: {}", e.getMessage());
        }
    }
    
    /**
     * 紧急字体诊断
     */
    private void performEmergencyFontDiagnostics() {
        log.error("=== 紧急字体诊断开始 ===");
        
        try {
            FontSettings fontSettings = FontSettings.getDefaultInstance();
            FontSourceBase[] sources = fontSettings.getFontsSources();
            
            log.error("当前字体源数量: {}", sources.length);
            for (FontSourceBase source : sources) {
                log.error("字体源: {}", source.getClass().getSimpleName());
            }
            
            if (tempFontDir != null) {
                File tempDir = new File(tempFontDir);
                if (tempDir.exists()) {
                    File[] fonts = tempDir.listFiles();
                    log.error("临时字体目录文件数量: {}", fonts != null ? fonts.length : 0);
                } else {
                    log.error("临时字体目录不存在: {}", tempFontDir);
                }
            }
            
        } catch (Exception e) {
            log.error("紧急字体诊断失败", e);
        }
        
        log.error("=== 紧急字体诊断结束 ===");
    }

    /**
     * 获取文件名，提供默认值
     */
    private String getFileName(AsposeConvertDTO dto) {
        String fileName = dto.getFileName();
        return fileName != null ? fileName : "output" + dto.getAsposeConvertType().getTargetSuffix();
    }

    /**
     * 替换文件后缀
     */
    private String replaceSuffix(String fileName, String newSuffix) {
        return fileName.replaceAll("\\.[^.]+$", newSuffix);
    }

    // 文本格式转换
    @Override
    public AsposeConvertVO executeDocxToTxtConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> {
            TxtSaveOptions options = new TxtSaveOptions();
            options.setEncoding(java.nio.charset.StandardCharsets.UTF_8);
            doc.save(out, options);
        });
    }

    @Override
    public AsposeConvertVO executeDocxToRtfConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> doc.save(out, dto.getAsposeConvertType().getTargetType()));
    }

    // 其他文档格式转换
    @Override
    public AsposeConvertVO executeDocxToOdtConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> doc.save(out, dto.getAsposeConvertType().getTargetType()));
    }

    @Override
    public AsposeConvertVO executeDocxToEpubConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> {
            HtmlSaveOptions options = new HtmlSaveOptions(dto.getAsposeConvertType().getTargetType());
            doc.save(out, options);
        });
    }

    @Override
    public AsposeConvertVO executeDocxToXpsConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> doc.save(out, dto.getAsposeConvertType().getTargetType()));
    }

    @Override
    public AsposeConvertVO executeDocxToMhtmlConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> {
            HtmlSaveOptions options = new HtmlSaveOptions(dto.getAsposeConvertType().getTargetType());
            doc.save(out, options);
        });
    }

    // 图片格式转换
    @Override
    public AsposeConvertVO executeDocxToJpegConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> {
            ImageSaveOptions options = new ImageSaveOptions(dto.getAsposeConvertType().getTargetType());
            options.setResolution(300);
            options.setJpegQuality(90);
            doc.save(out, options);
        });
    }

    @Override
    public AsposeConvertVO executeDocxToPngConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> {
            ImageSaveOptions options = new ImageSaveOptions(dto.getAsposeConvertType().getTargetType());
            options.setResolution(300);
            doc.save(out, options);
        });
    }

    @Override
    public AsposeConvertVO executeDocxToTiffConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> {
            ImageSaveOptions options = new ImageSaveOptions(dto.getAsposeConvertType().getTargetType());
            options.setResolution(300);
            doc.save(out, options);
        });
    }

    @Override
    public AsposeConvertVO executeDocxToBmpConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> {
            ImageSaveOptions options = new ImageSaveOptions(dto.getAsposeConvertType().getTargetType());
            options.setResolution(300);
            doc.save(out, options);
        });
    }

    @Override
    public AsposeConvertVO executeDocxToSvgConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> doc.save(out, dto.getAsposeConvertType().getTargetType()));
    }

    // 表格格式转换
    @Override
    public AsposeConvertVO executeDocxToXlsxConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> doc.save(out, dto.getAsposeConvertType().getTargetType()));
    }

    // 其他格式转换
    @Override
    public AsposeConvertVO executeDocxToMarkdownConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> {
            MarkdownSaveOptions options = new MarkdownSaveOptions();
            doc.save(out, options);
        });
    }

    // 反向转换
    @Override
    public AsposeConvertVO executeRtfToDocxConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> doc.save(out, dto.getAsposeConvertType().getTargetType()));
    }

    @Override
    public AsposeConvertVO executeTxtToDocxConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> doc.save(out, dto.getAsposeConvertType().getTargetType()));
    }

    @Override
    public AsposeConvertVO executeOdtToDocxConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> doc.save(out, dto.getAsposeConvertType().getTargetType()));
    }

    @Override
    public AsposeConvertVO executeDocToDocxConversion(AsposeConvertDTO dto) {
        return executeConversion(dto, (doc, out) -> doc.save(out, dto.getAsposeConvertType().getTargetType()));
    }

    /**
     * 转换处理器函数式接口
     */
    @FunctionalInterface
    private interface ConversionHandler {
        void convert(Document doc, ByteArrayOutputStream out) throws Exception;
    }

    /**
     * 执行字体可用性验证
     */
    private void performFontAvailabilityValidation() {
        log.info("▶ [可用性验证] 开始字体可用性验证...");
        
        // 验证中文字体可用性
        validateChineseFontAvailability();
        
        // 验证字体替换规则生效性
        validateFontSubstitutionRules();
        
        // 验证字体文件完整性
        validateFontFileIntegrity();
        
        log.info("✓ [可用性验证] 字体可用性验证完成");
    }

    /**
     * 验证中文字体可用性
     */
    private void validateChineseFontAvailability() {
        log.info("[INFO] 中文字体可用性检查:");
        
        String[] chineseFontTests = {
            "NotoSansSC-VF", 
            "SimSun", 
            "Microsoft YaHei",
            "HYZhongHeiTi-197"
        };
        
        for (String fontName : chineseFontTests) {
            try {
                // 检查字体是否在替换规则中
                boolean hasSubstitution = FONT_SUBSTITUTION_RULES.containsKey(fontName);
                String substitution = FONT_SUBSTITUTION_RULES.get(fontName);
                
                if (hasSubstitution) {
                    log.info("  • {} - ✓可用 (替换为: {})", fontName, substitution);
                } else {
                    // 检查是否为直接可用的字体文件
                    boolean isDirectFont = Arrays.asList(CHINESE_FONTS_PRIORITY).contains(fontName + ".ttf");
                    if (isDirectFont) {
                        log.info("  • {} - ✓可用 (直接字体文件)", fontName);
                    } else {
                        log.info("  • {} - ?未知 (无替换规则)", fontName);
                    }
                }
            } catch (Exception e) {
                log.warn("  • {} - ✗检查失败: {}", fontName, e.getMessage());
            }
        }
    }

    /**
     * 验证字体替换规则生效性
     */
    private void validateFontSubstitutionRules() {
        log.info("[INFO] 字体替换规则验证:");
        
        try {
            FontSettings fontSettings = FontSettings.getDefaultInstance();
            FontSubstitutionSettings substitutionSettings = fontSettings.getSubstitutionSettings();
            TableSubstitutionRule tableRule = substitutionSettings.getTableSubstitution();
            
            log.info("  • 表格替换规则: {}", tableRule.getEnabled() ? "✓已启用" : "✗未启用");
            
            FontInfoSubstitutionRule fontInfoRule = substitutionSettings.getFontInfoSubstitution();
            log.info("  • 字体信息替换: {}", fontInfoRule.getEnabled() ? "✓已启用" : "✗未启用");
            
            DefaultFontSubstitutionRule defaultRule = substitutionSettings.getDefaultFontSubstitution();
            log.info("  • 默认字体替换: {}", defaultRule.getEnabled() ? "✓已启用" : "✗未启用");
            
            if (defaultRule.getEnabled()) {
                log.info("  • 默认字体: {}", defaultRule.getDefaultFontName());
            }
            
        } catch (Exception e) {
            log.warn("[WARN] 字体替换规则验证失败", e);
        }
    }

    /**
     * 验证字体文件完整性
     */
    private void validateFontFileIntegrity() {
        log.info("[INFO] 字体文件完整性验证:");
        
        if (tempFontDir == null) {
            log.info("  • 使用本地字体目录，跳过临时文件验证");
            return;
        }
        
        File fontDir = new File(tempFontDir);
        if (!fontDir.exists()) {
            log.error("  • ✗临时字体目录不存在: {}", tempFontDir);
            return;
        }
        
        File[] fontFiles = fontDir.listFiles((dir, name) -> {
            String lowerName = name.toLowerCase();
            return lowerName.endsWith(".ttf") || lowerName.endsWith(".ttc") || lowerName.endsWith(".otf");
        });
        
        if (fontFiles == null || fontFiles.length == 0) {
            log.warn("  • ✗临时字体目录中无有效字体文件");
            return;
        }
        
        int validCount = 0;
        int invalidCount = 0;
        
        for (File fontFile : fontFiles) {
            try {
                if (fontFile.canRead() && fontFile.length() > 0) {
                    validCount++;
                    log.info("  • ✓ {} - 完整性验证通过", fontFile.getName());
                } else {
                    invalidCount++;
                    log.warn("  • ✗ {} - 文件不可读或为空", fontFile.getName());
                }
            } catch (Exception e) {
                invalidCount++;
                log.warn("  • ✗ {} - 验证失败: {}", fontFile.getName(), e.getMessage());
            }
        }
        
        log.info("  • 字体文件完整性: 有效 {} 个, 无效 {} 个", validCount, invalidCount);
        
        if (invalidCount > 0) {
            log.warn("[WARN] 发现 {} 个无效字体文件，可能影响字体渲染", invalidCount);
        }
    }

    /**
     * 判断是否为支持的字体文件名
     */
    private boolean isSupportedFontFileName(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return false;
        }
        
        String lowerName = fileName.toLowerCase();
        
        // 优先支持TTF格式
        for (String ext : FONT_EXTENSIONS) {
            if (lowerName.endsWith(ext)) {
                return true;
            }
        }
        
        // 警告不推荐的格式但仍然提取（作为备用）
        for (String ext : DEPRECATED_FONT_EXTENSIONS) {
            if (lowerName.endsWith(ext)) {
                log.warn("发现不推荐的字体格式: {} (Linux兼容性差)", fileName);
                return true; // 仍然提取作为备用
            }
        }
        
        return false;
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 字体资源信息类
     */
    private static class FontResourceInfo {
        private final Resource resource;
        private final String fileName;
        
        public FontResourceInfo(Resource resource, String fileName) {
            this.resource = resource;
            this.fileName = fileName;
        }
    }

    /**
     * 字体文件信息类
     */
    private static class FontFileInfo {
        private final String fileName;
        private final String extension;
        private final long fileSize;
        private final String formattedSize;
        private final boolean readable;
        
        public FontFileInfo(String fileName, String extension, long fileSize, String formattedSize, boolean readable) {
            this.fileName = fileName;
            this.extension = extension;
            this.fileSize = fileSize;
            this.formattedSize = formattedSize;
            this.readable = readable;
        }
        
        public String getFileName() { return fileName; }
        public String getExtension() { return extension; }
        public long getFileSize() { return fileSize; }
        public String getFormattedSize() { return formattedSize; }
        public boolean isReadable() { return readable; }
    }
}