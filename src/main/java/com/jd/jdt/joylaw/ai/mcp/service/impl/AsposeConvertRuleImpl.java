package com.jd.jdt.joylaw.ai.mcp.service.impl;

import com.jd.jdt.joylaw.ai.mcp.enums.AsposeConvertTypeEnum;
import com.jd.jdt.joylaw.ai.mcp.pojo.dto.AsposeConvertDTO;
import com.jd.jdt.joylaw.ai.mcp.pojo.vo.AsposeConvertVO;
import com.jd.jdt.joylaw.ai.mcp.service.AsposeConvertRule;
import com.jd.jdt.joylaw.ai.mcp.service.AsposeConvertService;
import jakarta.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.EnumMap;
import java.util.Map;
import java.util.function.Function;

/**
 * <p>
 * Aspose转换规则引擎实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Log4j2
@Service
public class AsposeConvertRuleImpl implements AsposeConvertRule {

    @Autowired
    private AsposeConvertService asposeConvertService;

    private Map<AsposeConvertTypeEnum, Function<AsposeConvertDTO, AsposeConvertVO>> convertRuleMap;

    @PostConstruct
    private void initializeConvertRules() {
        convertRuleMap = new EnumMap<>(AsposeConvertTypeEnum.class);
        
        // 基础转换规则
        convertRuleMap.put(AsposeConvertTypeEnum.DOCX_TO_PDF, asposeConvertService::executeDocxToPdfConversion);
        convertRuleMap.put(AsposeConvertTypeEnum.DOCX_TO_HTML, asposeConvertService::executeDocxToHtmlConversion);
        convertRuleMap.put(AsposeConvertTypeEnum.HTML_TO_DOCX, asposeConvertService::executeHtmlToDocxConversion);
        convertRuleMap.put(AsposeConvertTypeEnum.DOCX_TO_TXT, asposeConvertService::executeDocxToTxtConversion);
        convertRuleMap.put(AsposeConvertTypeEnum.DOCX_TO_RTF, asposeConvertService::executeDocxToRtfConversion);
        
        // 文档格式转换
        convertRuleMap.put(AsposeConvertTypeEnum.DOCX_TO_ODT, asposeConvertService::executeDocxToOdtConversion);
        convertRuleMap.put(AsposeConvertTypeEnum.DOCX_TO_EPUB, asposeConvertService::executeDocxToEpubConversion);
        convertRuleMap.put(AsposeConvertTypeEnum.DOCX_TO_XPS, asposeConvertService::executeDocxToXpsConversion);
        convertRuleMap.put(AsposeConvertTypeEnum.DOCX_TO_MHTML, asposeConvertService::executeDocxToMhtmlConversion);
        
        // 图片格式转换
        convertRuleMap.put(AsposeConvertTypeEnum.DOCX_TO_JPEG, asposeConvertService::executeDocxToJpegConversion);
        convertRuleMap.put(AsposeConvertTypeEnum.DOCX_TO_PNG, asposeConvertService::executeDocxToPngConversion);
        convertRuleMap.put(AsposeConvertTypeEnum.DOCX_TO_TIFF, asposeConvertService::executeDocxToTiffConversion);
        convertRuleMap.put(AsposeConvertTypeEnum.DOCX_TO_BMP, asposeConvertService::executeDocxToBmpConversion);
        convertRuleMap.put(AsposeConvertTypeEnum.DOCX_TO_SVG, asposeConvertService::executeDocxToSvgConversion);
        
        // 其他格式转换
        convertRuleMap.put(AsposeConvertTypeEnum.DOCX_TO_XLSX, asposeConvertService::executeDocxToXlsxConversion);
        convertRuleMap.put(AsposeConvertTypeEnum.DOCX_TO_MARKDOWN, asposeConvertService::executeDocxToMarkdownConversion);
        
        // 反向转换
        convertRuleMap.put(AsposeConvertTypeEnum.RTF_TO_DOCX, asposeConvertService::executeRtfToDocxConversion);
        convertRuleMap.put(AsposeConvertTypeEnum.TXT_TO_DOCX, asposeConvertService::executeTxtToDocxConversion);
        convertRuleMap.put(AsposeConvertTypeEnum.ODT_TO_DOCX, asposeConvertService::executeOdtToDocxConversion);
        convertRuleMap.put(AsposeConvertTypeEnum.DOC_TO_DOCX, asposeConvertService::executeDocToDocxConversion);
    }

    @Override
    public AsposeConvertVO executeConvertRule(AsposeConvertDTO asposeConvertDTO) throws IOException {
        // 统一参数验证
        validateRequest(asposeConvertDTO);
        
        // 获取转换规则
        Function<AsposeConvertDTO, AsposeConvertVO> convertRule = convertRuleMap.get(asposeConvertDTO.getAsposeConvertType());
        if (convertRule == null) {
            String errorMsg = "不支持的转换类型: " + asposeConvertDTO.getAsposeConvertType();
            log.error("规则引擎执行失败，{}", errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }

        try {
            // 执行转换
            AsposeConvertVO result = convertRule.apply(asposeConvertDTO);
            log.info("规则引擎执行成功：{} -> {}", 
                asposeConvertDTO.getAsposeConvertType().getSourceSuffix(),
                asposeConvertDTO.getAsposeConvertType().getTargetSuffix());
            return result;
        } catch (Exception e) {
            log.error("规则引擎执行失败: {}", e.getMessage(), e);
            // 直接抛出 IOException，避免不必要的包装
            throw (e instanceof IOException) ? (IOException) e : new IOException("规则引擎执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 统一参数验证
     */
    private void validateRequest(AsposeConvertDTO dto) {
        if (dto == null || dto.getByteArray() == null || dto.getAsposeConvertType() == null) {
            String errorMsg = dto == null ? "请求参数不能为空" : 
                             dto.getByteArray() == null ? "文件内容不能为空" : "转换类型不能为空";
            log.warn("规则引擎执行失败，{}", errorMsg);
            throw new IllegalArgumentException(errorMsg);
        }
    }
}