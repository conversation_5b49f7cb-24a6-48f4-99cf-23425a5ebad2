package com.jd.jdt.joylaw.ai.mcp.service;


import com.jd.jdt.joylaw.ai.mcp.enums.ChatRoleEnum;
import com.jd.jdt.joylaw.ai.mcp.pojo.dto.DoronChatDTO;
import com.jd.jdt.joylaw.ai.mcp.pojo.dto.DoronChatMessageDTO;
import com.jd.jdt.joylaw.ai.mcp.pojo.dto.DoronChatMessageDetailDTO;
import com.jd.jdt.joylaw.ai.mcp.rpc.feign.JoyEditService;
import lombok.extern.log4j.Log4j2;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * JoyEdit相关Tools实现
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Log4j2
@Service
public class JoyEditTollService {

    @Autowired
    private JoyEditService joyEditService;

    /**
     * 写作意图tool
     *
     * @param sessionId
     * @param userName
     * @param intentionDesc
     * @return
     */
    @Tool(name = "chatDoronJoyEdit", description = "用户有写作意图（基于模版和不需要模版写作）/改写/重写/优化等专业文档写作需求，例如帮我写一份销售合同/CRM PRD/文书，帮我改写/重写一下xxx的内容等")
    public String chatDoronJoyEdit(@ToolParam(description = "历史会话记录ID") String sessionId,
                                   @ToolParam(description = "用户名（例如用户的pin或erp等）") String userName,
                                   @ToolParam(description = "用户写作的具体意图描述") String intentionDesc) {
        DoronChatDTO doronChatDTO = new DoronChatDTO();
        doronChatDTO.setSessionId(sessionId);
        List<DoronChatMessageDTO> doronChatDTOUserList= new ArrayList<>();
        DoronChatMessageDTO doronChatMessageDTO = new DoronChatMessageDTO();
        List<DoronChatMessageDetailDTO> doronChatMessageDetailDTOList= new ArrayList<>();
        DoronChatMessageDetailDTO doronChatMessageDetailDTO = new DoronChatMessageDetailDTO();
        doronChatMessageDetailDTO.setMsgType(ChatRoleEnum.TEXT);
        doronChatMessageDetailDTO.setChatContent(intentionDesc);
        doronChatMessageDetailDTOList.add(doronChatMessageDetailDTO);
        doronChatMessageDTO.setChatRole(ChatRoleEnum.USER.getType());
        doronChatMessageDTO.setMessageDetailList(doronChatMessageDetailDTOList);
        doronChatDTOUserList.add(doronChatMessageDTO);
        doronChatDTO.setMessages(doronChatDTOUserList);
        doronChatDTO.setStream(Boolean.FALSE);
        return joyEditService.chatDoronStream(userName, doronChatDTO);
    }


}
