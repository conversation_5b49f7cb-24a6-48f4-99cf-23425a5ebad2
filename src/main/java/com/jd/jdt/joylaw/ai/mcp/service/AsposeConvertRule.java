package com.jd.jdt.joylaw.ai.mcp.service;

import com.jd.jdt.joylaw.ai.mcp.pojo.dto.AsposeConvertDTO;
import com.jd.jdt.joylaw.ai.mcp.pojo.vo.AsposeConvertVO;

import java.io.IOException;

/**
 * <p>
 * Aspose转换规则引擎接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
public interface AsposeConvertRule {

    /**
     * 执行文件格式转换规则
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     * @throws IOException 转换异常
     */
    AsposeConvertVO executeConvertRule(AsposeConvertDTO asposeConvertDTO) throws IOException;

}