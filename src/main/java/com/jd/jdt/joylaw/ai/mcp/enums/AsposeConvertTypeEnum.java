package com.jd.jdt.joylaw.ai.mcp.enums;

/**
 * <p>
 * Office 文件转换类型枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public enum AsposeConvertTypeEnum {

    DOCX_TO_HTML(".docx", ".html", 50, "docx转html"),     // SaveFormat.HTML = 50
    HTML_TO_DOCX(".html", ".docx", 20, "html转word"),    // SaveFormat.DOCX = 20
    DOCX_TO_PDF(".docx", ".pdf", 40, "docx转pdf"),       // SaveFormat.PDF = 40

    // 文本格式转换
    DOCX_TO_TXT(".docx", ".txt", 70, "docx转txt"),        // SaveFormat.TEXT = 70
    DOCX_TO_RTF(".docx", ".rtf", 30, "docx转rtf"),        // SaveFormat.RTF = 30

    // 其他文档格式转换
    DOCX_TO_ODT(".docx", ".odt", 60, "docx转odt"),        // SaveFormat.ODT = 60
    DOCX_TO_EPUB(".docx", ".epub", 52, "docx转epub"),     // SaveFormat.EPUB = 52
    DOCX_TO_XPS(".docx", ".xps", 41, "docx转xps"),        // SaveFormat.XPS = 41
    DOCX_TO_MHTML(".docx", ".mhtml", 51, "docx转mhtml"),  // SaveFormat.MHTML = 51

    // 图片格式转换
    DOCX_TO_JPEG(".docx", ".jpeg", 104, "docx转jpeg"),    // SaveFormat.JPEG = 104
    DOCX_TO_PNG(".docx", ".png", 101, "docx转png"),       // SaveFormat.PNG = 101
    DOCX_TO_TIFF(".docx", ".tiff", 100, "docx转tiff"),    // SaveFormat.TIFF = 100
    DOCX_TO_BMP(".docx", ".bmp", 102, "docx转bmp"),       // SaveFormat.BMP = 102
    DOCX_TO_SVG(".docx", ".svg", 44, "docx转svg"),        // SaveFormat.SVG = 44

    // 表格格式转换
    DOCX_TO_XLSX(".docx", ".xlsx", 80, "docx转xlsx"),     // SaveFormat.XLSX = 80

    // 其他格式转换
    DOCX_TO_MARKDOWN(".docx", ".md", 73, "docx转markdown"), // SaveFormat.MARKDOWN = 73</search>

    // 反向转换
    RTF_TO_DOCX(".rtf", ".docx", 20, "rtf转docx"),        // SaveFormat.DOCX = 20
    TXT_TO_DOCX(".txt", ".docx", 20, "txt转docx"),        // SaveFormat.DOCX = 20
    ODT_TO_DOCX(".odt", ".docx", 20, "odt转docx"),        // SaveFormat.DOCX = 20
    DOC_TO_DOCX(".doc", ".docx", 20, "doc转docx");        // SaveFormat.DOCX = 20

    private String sourceSuffix;
    private String targetSuffix;
    private int targetType;
    private String subDesc;

    AsposeConvertTypeEnum(String sourceSuffix, String targetSuffix, int targetType, String subDesc) {
        this.sourceSuffix = sourceSuffix;
        this.targetSuffix = targetSuffix;
        this.targetType = targetType;
        this.subDesc = subDesc;
    }

    public String getSourceSuffix() {
        return sourceSuffix;
    }

    public String getTargetSuffix() {
        return targetSuffix;
    }

    public int getTargetType() {
        return targetType;
    }

    public String getSubDesc() {
        return subDesc;
    }
} 