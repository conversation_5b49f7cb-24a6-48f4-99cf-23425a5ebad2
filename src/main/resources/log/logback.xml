<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 全局变量（Properties） -->
    <property name="log.level.console" value="INFO"/>
    <property name="project.name" value="${project.artifactId}"/>
    <property name="log.path" value="/export/Logs/app/${scripts_packageName}"/>
<!--        <property name="log.path" value="/Users/<USER>/IdeaProjects/mcp-server/${scripts_packageName}"/>-->
    <!--    <property name="log.pattern" value="%clr{%d{yyyy-MM-dd HH:mm:ss.SSS}}:%clr{[TId :%T]}{blue} %clr{%-5p}{cyan} %clr{${sys:PID}}{magenta} %clr{-&#45;&#45;} %clr{%c.%M:%L}{cyan} %clr{:}{faint} %m%n%xwEx" />-->
    <property name="log.pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5p %c{1}:%L - %m%n"/>
    <!-- 控制台输出（Console Appender） -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <target>SYSTEM_OUT</target>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <!-- 滚动文件输出（RollingFile Appender） -->
    <appender name="ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/${project.name}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/${project.name}_detail.log%d{yyyy-MM-dd}</fileNamePattern>
            <maxHistory>100</maxHistory> <!-- 最多保留100个日志文件 -->
            <cleanHistoryOnStart>true</cleanHistoryOnStart> <!-- 启动时清理旧日志 -->
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <!-- 错误日志滚动文件（RollingFile Appender，仅记录 ERROR 级别） -->
    <appender name="EXCEPTION_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/${project.name}_error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/${project.name}_error.log%d{yyyy-MM-dd}</fileNamePattern>
            <maxHistory>100</maxHistory> <!-- 最多保留100个日志文件 -->
            <cleanHistoryOnStart>true</cleanHistoryOnStart> <!-- 启动时清理旧日志 -->
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level> <!-- 只记录 ERROR 级别及以上 -->
        </filter>
    </appender>

    <!-- 日志级别配置（Loggers） -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ROLLING_FILE"/>
        <appender-ref ref="EXCEPTION_ROLLING_FILE"/>
    </root>

    <!-- Spring Web 相关日志配置 -->
    <logger name="org.springframework.web" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ROLLING_FILE"/>
    </logger>

    <!-- MyBatis-Plus 日志配置 -->
    <logger name="com.baomidou.mybatisplus" level="DEBUG"/>
</configuration>