server:
  port: 8081
  max-http-header-size: 100MB
  tomcat:
    max-http-post-size: 100MB
    max-swallow-size: 100MB
spring:
  cloud:
    compatibility-verifier:
      enabled: false
  main:
    banner-mode: log
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      file-size-threshold: 10MB
      location: /tmp
  http:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  jackson:
    serialization:
      fail-on-empty-beans: false
    default-property-inclusion: non_null

logging:
  config: classpath:log/logback.xml
#  pattern:
#    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
#  file:
#    name: /export/Logs/app/@scripts_packageName@/@project.artifactId@.log

# transport.mode=stdio
transport:
  mode: sse

rpc:
  joyeidt:
    url: https://joyedit-dev.jd.com
#    url: http://joyedit-local.jd.com:8091/

aiMcpApiKey: 1234567890